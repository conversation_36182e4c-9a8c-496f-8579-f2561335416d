// NILA Sales Landing Page - Main JavaScript
// Author: NILA Team
// Version: 1.0.0

// Configuration - using environment variables for security
const CONFIG = {
    calcom: {
        url: import.meta.env.VITE_CALCOM_URL || 'https://cal.com/nila-poetry-of-light/nila-demo',
        apiToken: import.meta.env.VITE_CALCOM_API_TOKEN,
        baseUrl: import.meta.env.VITE_CALCOM_BASE_URL || 'https://api.cal.com/v2',
        username: import.meta.env.VITE_CALCOM_USERNAME || 'nila-poetry-of-light',
        eventTypeSlug: import.meta.env.VITE_CALCOM_EVENT_SLUG || 'nila-demo'
    },
    resend: {
        apiKey: import.meta.env.VITE_RESEND_API_KEY,
        fromEmail: 'NILA Landing <<EMAIL>>',
        toEmail: '<EMAIL>',
        enabled: true
    },
    webhook: {
        url: import.meta.env.VITE_WEBHOOK_URL,
        apiKey: import.meta.env.VITE_WEBHOOK_API_KEY,
        retryAttempts: 3,
        retryDelay: 1000
    },
    formspree: {
        endpoint: import.meta.env.VITE_FORMSPREE_ENDPOINT || 'https://formspree.io/f/YOUR_FORM_ID',
        enabled: true
    },
    analytics: {
        googleAnalytics: {
            enabled: false,
            measurementId: null
        },
        facebookPixel: {
            enabled: false,
            pixelId: null
        },
        hotjar: {
            enabled: false,
            siteId: null
        }
    }
};

// Cal.com integration - clean implementation

// Define global functions immediately
window.openGroupDemo = function() {
    console.log('🗓️ Opening Cal.com booking...');

    // Track event if available
    if (typeof trackEvent === 'function') {
        trackEvent('cta_click', {
            cta_type: 'group_demo',
            cta_location: 'hero'
        });
    }

    // Use Cal.com data attributes method (automatic popup)
    try {
        console.log('✅ Triggering Cal.com popup via data attributes...');

        // Create a temporary element with Cal.com data attributes
        const tempButton = document.createElement('button');
        tempButton.setAttribute('data-cal-link', 'nila-poetry-of-light/nila-demo');
        tempButton.setAttribute('data-cal-config', '{"layout":"month_view","theme":"light"}');
        tempButton.style.display = 'none';
        document.body.appendChild(tempButton);

        // Trigger click on the temp button
        tempButton.click();

        // Remove temp button
        setTimeout(() => {
            document.body.removeChild(tempButton);
        }, 100);

        // Apply custom styling after popup opens
        setTimeout(() => {
            applyCustomCalcomStyles();
        }, 500);

    } catch (error) {
        console.error('❌ Cal.com error:', error);
        console.log('🔄 Falling back to direct URL...');
        window.open('https://cal.com/nila-poetry-of-light/nila-demo', '_blank');
    }
};

window.openSampleForm = function() {
    console.log('📦 Opening sample form...');

    // Track event if available
    if (typeof trackEvent === 'function') {
        trackEvent('cta_click', {
            cta_type: 'sample_request',
            cta_location: 'hero'
        });
    }

    // Find modal elements
    const sampleModal = document.getElementById('sample-modal');
    if (sampleModal) {
        sampleModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Focus first input
        const firstInput = sampleModal.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
};

window.closeSampleForm = function() {
    console.log('❌ Closing sample form...');

    const sampleModal = document.getElementById('sample-modal');
    if (sampleModal) {
        sampleModal.classList.add('hidden');
        document.body.style.overflow = '';
    }
};

// Apply custom styling to Cal.com popup
function applyCustomCalcomStyles() {
    try {
        // Find Cal.com popup elements
        const popup = document.querySelector('[data-cal-namespace]');
        const overlay = document.querySelector('.cal-modal-overlay, [data-cal-namespace] + div');
        const iframe = document.querySelector('[data-cal-namespace] iframe');

        if (popup) {
            // Enhanced popup styling
            const popupContainer = popup.querySelector('div');
            if (popupContainer) {
                popupContainer.style.background = '#F8F4EC';
                popupContainer.style.borderRadius = '1.5rem';
                popupContainer.style.overflow = 'hidden';
                popupContainer.style.boxShadow = '0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3)';
                popupContainer.style.border = 'none';
                popupContainer.style.maxWidth = '85vw';
                popupContainer.style.maxHeight = '85vh';
            }
        }

        if (overlay) {
            overlay.style.background = 'rgba(55, 53, 52, 0.85)';
            overlay.style.backdropFilter = 'blur(12px)';
            overlay.style.webkitBackdropFilter = 'blur(12px)';
        }

        if (iframe) {
            iframe.style.borderRadius = '1.5rem';
            iframe.style.background = '#F8F4EC';

            // Try to style iframe content when it loads
            iframe.addEventListener('load', () => {
                try {
                    injectIframeStyles(iframe);
                } catch (e) {
                    console.log('Cannot access iframe content (CORS)');
                }
            });
        }

        // Enhanced close button styling
        const closeButtons = document.querySelectorAll('[data-cal-namespace] button[aria-label*="close"], [data-cal-namespace] .close, [data-cal-namespace] [data-testid="close"]');
        closeButtons.forEach(closeButton => {
            closeButton.style.background = '#F8F4EC';
            closeButton.style.color = '#373534';
            closeButton.style.borderRadius = '50%';
            closeButton.style.width = '44px';
            closeButton.style.height = '44px';
            closeButton.style.border = '2px solid #DCC8B6';
            closeButton.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            closeButton.style.boxShadow = '0 4px 12px rgba(55, 53, 52, 0.15)';
            closeButton.style.position = 'absolute';
            closeButton.style.top = '1rem';
            closeButton.style.right = '1rem';
            closeButton.style.zIndex = '10';

            closeButton.addEventListener('mouseenter', () => {
                closeButton.style.background = '#CFB5A7';
                closeButton.style.transform = 'scale(1.05)';
                closeButton.style.borderColor = '#CFB5A7';
                closeButton.style.boxShadow = '0 8px 20px rgba(207, 181, 167, 0.4)';
            });

            closeButton.addEventListener('mouseleave', () => {
                closeButton.style.background = '#F8F4EC';
                closeButton.style.transform = 'scale(1)';
                closeButton.style.borderColor = '#DCC8B6';
                closeButton.style.boxShadow = '0 4px 12px rgba(55, 53, 52, 0.15)';
            });
        });

        console.log('✅ Enhanced Cal.com styles applied');
    } catch (error) {
        console.error('❌ Error applying custom Cal.com styles:', error);
    }
}

// Try to inject styles into iframe content (if CORS allows)
function injectIframeStyles(iframe) {
    try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        if (!iframeDoc) return;

        const customIframeCSS = `
            <style>
                /* NILA Custom styles for Cal.com iframe content */
                body {
                    font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    background: #F8F4EC !important;
                }

                /* Header styling */
                h1, h2, h3 {
                    color: #373534 !important;
                    font-family: 'IBM Plex Sans', sans-serif !important;
                }

                /* Button styling */
                button[data-testid*="time"] {
                    border: 2px solid #DCC8B6 !important;
                    background: #F8F4EC !important;
                    color: #373534 !important;
                    border-radius: 0.75rem !important;
                    transition: all 0.3s ease !important;
                }

                button[data-testid*="time"]:hover {
                    background: #CFB5A7 !important;
                    border-color: #CFB5A7 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 4px 12px rgba(207, 181, 167, 0.3) !important;
                }

                /* Selected date styling */
                [data-testid*="day"][aria-selected="true"] {
                    background: #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                }

                /* Primary buttons */
                button[type="submit"], .btn-primary {
                    background: #CFB5A7 !important;
                    border: 2px solid #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                    font-weight: 600 !important;
                    transition: all 0.3s ease !important;
                }

                button[type="submit"]:hover, .btn-primary:hover {
                    background: #373534 !important;
                    border-color: #373534 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 8px 20px rgba(55, 53, 52, 0.3) !important;
                }
            </style>
        `;

        // Inject the styles
        iframeDoc.head.insertAdjacentHTML('beforeend', customIframeCSS);
        console.log('✅ Iframe styles injected');
    } catch (error) {
        console.log('Cannot inject iframe styles (CORS restriction)');
    }
}

// Inject custom CSS for Cal.com popup
function injectCalcomCustomCSS() {
    // Check if custom CSS is already injected
    if (document.getElementById('nila-calcom-custom-css')) {
        return;
    }

    const customCSS = `
        /* NILA Custom Cal.com Styling - Enhanced */
        [data-cal-namespace] {
            z-index: 9999 !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        /* Main popup container */
        [data-cal-namespace] > div {
            border-radius: 1.5rem !important;
            box-shadow: 0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3) !important;
            overflow: hidden !important;
            border: none !important;
            max-width: 85vw !important;
            max-height: 85vh !important;
            background: #F8F4EC !important;
        }

        /* Backdrop overlay */
        [data-cal-namespace] + div {
            background: rgba(55, 53, 52, 0.85) !important;
            backdrop-filter: blur(12px) !important;
            -webkit-backdrop-filter: blur(12px) !important;
        }

        /* Main iframe styling */
        [data-cal-namespace] iframe {
            border-radius: 1.5rem !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            background: #F8F4EC !important;
        }

        /* Close button styling */
        [data-cal-namespace] button[aria-label*="close"],
        [data-cal-namespace] .close,
        [data-cal-namespace] [data-testid="close"] {
            background: #F8F4EC !important;
            color: #373534 !important;
            border-radius: 50% !important;
            width: 44px !important;
            height: 44px !important;
            border: 2px solid #DCC8B6 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 4px 12px rgba(55, 53, 52, 0.15) !important;
            position: absolute !important;
            top: 1rem !important;
            right: 1rem !important;
            z-index: 10 !important;
        }

        [data-cal-namespace] button[aria-label*="close"]:hover,
        [data-cal-namespace] .close:hover,
        [data-cal-namespace] [data-testid="close"]:hover {
            background: #CFB5A7 !important;
            transform: scale(1.05) !important;
            border-color: #CFB5A7 !important;
            box-shadow: 0 8px 20px rgba(207, 181, 167, 0.4) !important;
        }

        /* Header styling inside iframe */
        [data-cal-namespace] iframe[src*="cal.com"] {
            /* Custom CSS will be injected into iframe content */
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            [data-cal-namespace] > div {
                margin: 0.75rem !important;
                max-width: calc(100vw - 1.5rem) !important;
                max-height: calc(100vh - 1.5rem) !important;
                border-radius: 1.25rem !important;
            }

            [data-cal-namespace] button[aria-label*="close"],
            [data-cal-namespace] .close,
            [data-cal-namespace] [data-testid="close"] {
                width: 40px !important;
                height: 40px !important;
                top: 0.75rem !important;
                right: 0.75rem !important;
            }
        }

        /* Animation for popup appearance */
        [data-cal-namespace] > div {
            animation: nilaPopupAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        @keyframes nilaPopupAppear {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Enhanced backdrop animation */
        [data-cal-namespace] + div {
            animation: nilaBackdropAppear 0.3s ease-out !important;
        }

        @keyframes nilaBackdropAppear {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
    `;

    const styleElement = document.createElement('style');
    styleElement.id = 'nila-calcom-custom-css';
    styleElement.textContent = customCSS;
    document.head.appendChild(styleElement);

    console.log('✅ Custom Cal.com CSS injected');
}

// Monitor for Cal.com popup and apply styling
function initializeCalcomStyleMonitor() {
    // Monitor for Cal.com popup creation
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if Cal.com popup was added
                    if (node.hasAttribute && node.hasAttribute('data-cal-namespace')) {
                        console.log('🎨 Cal.com popup detected, applying custom styles...');
                        setTimeout(() => {
                            applyCustomCalcomStyles();
                        }, 100);
                    }

                    // Also check child elements
                    if (node.querySelector && node.querySelector('[data-cal-namespace]')) {
                        console.log('🎨 Cal.com popup detected in child, applying custom styles...');
                        setTimeout(() => {
                            applyCustomCalcomStyles();
                        }, 100);
                    }
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('👀 Cal.com style monitor initialized');
}

// DOM Elements
const elements = {
    sampleModal: document.getElementById('sample-modal'),
    sampleForm: document.getElementById('sample-form'),
    submitButton: document.getElementById('submit-sample-form'),
    submitText: document.getElementById('submit-text'),
    submitLoading: document.getElementById('submit-loading'),
    floatingCta: document.getElementById('floating-cta'),
    stickyCtaBar: document.getElementById('sticky-cta-bar')
};

// IMMEDIATE SCROLL TEST - Add right away to test if scroll events work at all
console.log('🧪 Adding immediate scroll test...');
window.addEventListener('scroll', function() {
    console.log('🔄 IMMEDIATE SCROLL TEST - Position:', window.scrollY);
});
console.log('✅ Immediate scroll test added');

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Main initialization function
function initializeApp() {
    console.log('🎨 NILA Landing Page - Initializing...');

    // Force close any open modals
    if (elements.sampleModal) {
        elements.sampleModal.classList.add('hidden');
        document.body.style.overflow = '';
    }

    // Initialize scroll animations
    initializeScrollAnimations();

    // Initialize form validation
    initializeFormValidation();

    // Initialize analytics
    initializeAnalytics();

    // Initialize event listeners
    initializeEventListeners();

    // Initialize floating CTA
    initializeFloatingCTA();

    // Initialize sticky CTA bar
    console.log('🚀 About to initialize sticky CTA bar...');
    initializeStickyCtaBar();
    console.log('✅ Sticky CTA bar initialization completed');

    // Initialize hero parallax effect
    initializeHeroParallax();

    // Initialize Cal.com integration
    initializeCalcomIntegration();

    // Inject custom CSS for Cal.com
    injectCalcomCustomCSS();

    // Monitor for Cal.com popup
    initializeCalcomStyleMonitor();

    console.log('✅ NILA Landing Page - Ready!');
}

// Scroll animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Initialize counter animations for stats
    initializeCounterAnimations();

    // Special handling for Nederlands vakmanschap image
    const nederlandsVakmanschapImage = document.querySelector('.nederlands-vakmanschap-image');
    if (nederlandsVakmanschapImage) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    console.log('Nederlands vakmanschap image is in view - applying zoom');
                    entry.target.style.transform = 'scale(1.15)';
                    entry.target.style.objectPosition = '33% 50%';
                } else {
                    entry.target.style.transform = 'scale(1.0)';
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -100px 0px'
        });

        imageObserver.observe(nederlandsVakmanschapImage);
    }
}

// Counter animations for statistics
function initializeCounterAnimations() {
    const counters = document.querySelectorAll('[data-count-to]');
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                entry.target.classList.add('counted');
                animateCounter(entry.target);
            }
        });
    }, {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Animate individual counter
function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count-to'));
    const suffix = element.getAttribute('data-count-suffix') || '';
    const duration = 2000; // 2 seconds
    const startTime = performance.now();
    const startValue = 0;

    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (target - startValue) * easeOutQuart);

        element.textContent = currentValue + suffix;

        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target + suffix;
        }
    }

    requestAnimationFrame(updateCounter);
}

// Form validation
function initializeFormValidation() {
    const form = elements.sampleForm;
    if (!form) return;

    // Real-time validation
    const inputs = form.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

// Validate individual field
function validateField(event) {
    const field = event.target;
    const value = field.value.trim();

    // Remove existing error styling
    field.classList.remove('border-red-500');

    // Validation rules
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'Dit veld is verplicht');
        return false;
    }

    if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Voer een geldig e-mailadres in');
        return false;
    }

    if (field.name === 'postalCode' && value && !isValidPostalCode(value)) {
        showFieldError(field, 'Voer een geldige postcode in');
        return false;
    }

    return true;
}

// Clear field error
function clearFieldError(event) {
    const field = event.target;
    field.classList.remove('border-red-500');

    // Remove error message
    const errorMsg = field.parentNode.querySelector('.error-message');
    if (errorMsg) {
        errorMsg.remove();
    }
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('border-red-500');

    // Remove existing error message
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }

    // Add new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-500 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Postal code validation (Dutch format)
function isValidPostalCode(postalCode) {
    const dutchPostalRegex = /^[1-9][0-9]{3}\s?[A-Za-z]{2}$/;
    return dutchPostalRegex.test(postalCode);
}

// Event listeners
function initializeEventListeners() {
    // Sample form submission
    if (elements.sampleForm) {
        elements.sampleForm.addEventListener('submit', handleSampleFormSubmit);
    }

    // Modal close on outside click
    if (elements.sampleModal) {
        elements.sampleModal.addEventListener('click', (e) => {
            if (e.target === elements.sampleModal) {
                closeSampleForm();
            }
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !elements.sampleModal.classList.contains('hidden')) {
            closeSampleForm();
        }
    });
}

// Initialize hero parallax effect
function initializeHeroParallax() {
    const heroImage = document.querySelector('.hero-image');
    if (!heroImage) return;

    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const heroSection = document.getElementById('hero');
        const heroHeight = heroSection.offsetHeight;

        // Only apply parallax while hero is visible
        if (scrolled < heroHeight) {
            const parallaxSpeed = 0.5;
            const yPos = scrolled * parallaxSpeed;

            // Apply subtle parallax transform with correct scale
            heroImage.style.transform = `scale(1.4) translateY(${yPos}px)`;

            // Subtle opacity change for depth
            const opacity = Math.max(0.8, 1 - (scrolled / heroHeight) * 0.2);
            heroImage.style.opacity = opacity;
        }

        ticking = false;
    }

    function handleParallaxScroll() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    // Add scroll listener for parallax
    window.addEventListener('scroll', handleParallaxScroll, { passive: true });
}

// Initialize floating CTA
function initializeFloatingCTA() {
    if (!elements.floatingCta) return;

    let isVisible = false;

    function toggleFloatingCTA() {
        const scrollPosition = window.scrollY;
        const heroHeight = window.innerHeight; // Approximately hero section height

        if (scrollPosition > heroHeight && !isVisible) {
            elements.floatingCta.classList.remove('hidden');
            isVisible = true;

            // Track floating CTA appearance
            trackEvent('floating_cta_shown', {
                scroll_position: scrollPosition
            });
        } else if (scrollPosition <= heroHeight && isVisible) {
            elements.floatingCta.classList.add('hidden');
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleFloatingCTA();
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', handleScroll);

    // Track floating CTA clicks
    elements.floatingCta.addEventListener('click', () => {
        trackEvent('floating_cta_click', {
            cta_type: 'floating_demo_button'
        });
    });
}

// Initialize sticky CTA bar
function initializeStickyCtaBar() {
    console.log('🔧 Initializing sticky CTA bar...');

    // Try multiple ways to find the element
    const stickyBar = document.getElementById('sticky-cta-bar') || elements.stickyCtaBar;

    if (!stickyBar) {
        console.error('❌ Sticky CTA bar element not found!');
        console.log('Available elements:', Object.keys(elements));
        return;
    }

    console.log('✅ Sticky CTA bar element found:', stickyBar);

    // Update elements reference
    elements.stickyCtaBar = stickyBar;

    let isVisible = false;
    let hasBeenShown = false;

    function toggleStickyCtaBar() {
        const scrollPosition = window.scrollY;
        const isMobile = window.innerWidth < 769;

        // Best practice: Show after 25-30% scroll (much earlier than hero height)
        const triggerPoint = isMobile ? 200 : 300; // Fixed pixel values work better

        const documentHeight = document.documentElement.scrollHeight;
        const windowHeight = window.innerHeight;
        const footerOffset = 200; // Hide when near footer

        // Don't show if near bottom of page
        const nearBottom = scrollPosition + windowHeight > documentHeight - footerOffset;

        // Always log scroll events to see if they're working
        console.log('🔄 SCROLL EVENT DETECTED:', {
            scrollPosition,
            triggerPoint,
            isVisible,
            nearBottom,
            isMobile,
            shouldShow: scrollPosition > triggerPoint && !isVisible && !nearBottom,
            elementExists: !!elements.stickyCtaBar
        });

        if (scrollPosition > triggerPoint && !isVisible && !nearBottom) {
            console.log('✅ Showing sticky CTA bar');
            elements.stickyCtaBar.classList.remove('hidden');
            elements.stickyCtaBar.classList.remove('translate-y-full'); // Remove transform class
            elements.stickyCtaBar.style.transform = 'translateY(0)';
            elements.stickyCtaBar.style.display = 'block'; // Force display override
            isVisible = true;

            // Track first appearance
            if (!hasBeenShown) {
                trackEvent('sticky_cta_shown', {
                    scroll_position: scrollPosition,
                    is_mobile: isMobile,
                    trigger_point: triggerPoint
                });
                hasBeenShown = true;
            }
        } else if ((scrollPosition <= triggerPoint || nearBottom) && isVisible) {
            console.log('❌ Hiding sticky CTA bar');
            elements.stickyCtaBar.classList.add('translate-y-full'); // Add transform class back
            elements.stickyCtaBar.style.transform = 'translateY(100%)';
            setTimeout(() => {
                if (!isVisible) elements.stickyCtaBar.classList.add('hidden');
            }, 300);
            isVisible = false;
        }
    }

    // Throttle scroll events for performance
    let ticking = false;
    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleStickyCtaBar();
                ticking = false;
            });
            ticking = true;
        }
    }

    // Add scroll listener
    window.addEventListener('scroll', handleScroll);
    console.log('✅ Sticky CTA bar scroll listener added');

    // Test scroll listener is working
    window.addEventListener('scroll', () => {
        console.log('🔄 Basic scroll event detected at:', window.scrollY);
    });

    // Test initial state
    console.log('🧪 Initial scroll position:', window.scrollY);
    console.log('🧪 Initial trigger test...');
    toggleStickyCtaBar();
}

// Hide sticky bar (user action)
window.hideStickyBar = function() {
    if (elements.stickyCtaBar) {
        elements.stickyCtaBar.style.transform = 'translateY(100%)';
        setTimeout(() => {
            elements.stickyCtaBar.classList.add('hidden');
        }, 300);

        trackEvent('sticky_cta_dismissed', {
            user_action: true
        });
    }
};

// Cal.com Integration - Clean Implementation
function initializeCalcomIntegration() {
    console.log('🗓️ Initializing Cal.com integration...');

    // Initialize Cal.com embed
    if (typeof Cal !== 'undefined') {
        Cal("init", {
            origin: "https://app.cal.com"
        });

        Cal("ui", {
            "styles": {
                "branding": {
                    "brandColor": "#CFB5A7"
                }
            },
            "hideEventTypeDetails": false,
            "layout": "month_view"
        });

        console.log('✅ Cal.com initialized');
    } else {
        console.log('⚠️ Cal.com script not loaded yet');
    }

    // Set up demo times
    updateDemoTimes();
    setInterval(updateDemoTimes, 10 * 60 * 1000);

    console.log('✅ Cal.com integration initialized');
}

async function updateDemoTimes() {
    console.log('🗓️ Updating demo times from Cal.com API...');

    try {
        // Calculate date range (next 7 days)
        const startDate = new Date().toISOString().split('T')[0]; // Today in YYYY-MM-DD format
        const endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 7 days from now

        // Fetch available slots from Cal.com API v2
        const response = await fetch(`${CONFIG.calcom.baseUrl}/slots?username=${CONFIG.calcom.username}&eventTypeSlug=${CONFIG.calcom.eventTypeSlug}&start=${startDate}&end=${endDate}&timeZone=Europe/Amsterdam`, {
            headers: {
                'Authorization': `Bearer ${CONFIG.calcom.apiToken}`,
                'cal-api-version': '2024-09-04',
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('✅ Cal.com API response:', data);

            if (data.status === 'success' && data.data) {
                // Find the first available slot from all dates
                let nextSlot = null;
                let nextSlotDate = null;

                for (const [dateStr, slots] of Object.entries(data.data)) {
                    if (slots && slots.length > 0) {
                        nextSlot = slots[0];
                        nextSlotDate = new Date(nextSlot.start);
                        break;
                    }
                }

                if (nextSlot && nextSlotDate) {
                    // Update demo time elements
                    const demoTimeElements = document.querySelectorAll('[data-demo-time]');
                    demoTimeElements.forEach(element => {
                        const format = element.getAttribute('data-demo-format');
                        if (format === 'week') {
                            element.textContent = `Volgende demonstratie ${formatDateRelative(nextSlotDate)}`;
                        } else if (format === 'short') {
                            element.textContent = formatDateShort(nextSlotDate);
                        } else {
                            element.textContent = formatDateTime(nextSlotDate);
                        }
                    });

                    // Update availability elements with dynamic message
                    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
                    availabilityElements.forEach(element => {
                        // Check if this is the hero urgency element
                        if (element.hasAttribute('data-hero-urgency')) {
                            // Hero section keeps urgency message
                            const totalSlots = Object.values(data.data).reduce((total, daySlots) => total + daySlots.length, 0);
                            if (totalSlots <= 3) {
                                element.textContent = 'Nog slechts 3 plaatsen beschikbaar';
                            } else if (totalSlots <= 10) {
                                element.textContent = 'Beperkte plaatsen beschikbaar';
                            } else {
                                element.textContent = 'Plaatsen beschikbaar';
                            }
                        } else {
                            // Other sections show discount message
                            element.textContent = 'Neem deel en ontvang een kortingscode';
                        }
                    });

                    // Update urgency message
                    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
                    urgencyElements.forEach(element => {
                        // Skip hero urgency elements - they should keep their original text
                        if (element.closest('.btn-primary-hero') || element.closest('.hero-cta-buttons') || element.closest('[data-hero-urgency]')) {
                            return;
                        }

                        const totalSlots = Object.values(data.data).reduce((total, daySlots) => total + daySlots.length, 0);
                        if (totalSlots <= 3) {
                            element.textContent = 'Neem deel en ontvang een kortingscode';
                            element.classList.add('text-green-400');
                        } else if (totalSlots <= 10) {
                            element.textContent = 'Neem deel en ontvang een kortingscode';
                            element.classList.add('text-green-400');
                        } else {
                            element.textContent = 'Neem deel en ontvang een kortingscode';
                            element.classList.add('text-green-400');
                        }
                    });

                    console.log('✅ Demo times updated from Cal.com API');
                    return;
                } else {
                    console.log('⚠️ No available slots found in Cal.com API response');
                }
            }
        } else {
            console.warn('⚠️ Cal.com API error:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('❌ Error fetching Cal.com data:', error);
    }

    // Fallback to static demo times
    console.log('🔄 Using fallback demo times...');
    updateFallbackDemoTimes();
}

function updateFallbackDemoTimes() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);

    const demoTimeElements = document.querySelectorAll('[data-demo-time]');
    demoTimeElements.forEach(element => {
        const format = element.getAttribute('data-demo-format');
        if (format === 'week') {
            element.textContent = 'Volgende demonstratie morgen';
        } else if (format === 'short') {
            element.textContent = 'Morgen 14:00';
        } else {
            element.textContent = 'Morgen 14:00';
        }
    });

    const availabilityElements = document.querySelectorAll('[data-demo-availability]');
    availabilityElements.forEach(element => {
        // Check if this is the hero urgency element
        if (element.hasAttribute('data-hero-urgency')) {
            // Hero section keeps urgency message
            element.textContent = 'Nog slechts 3 plaatsen beschikbaar';
        } else {
            // Other sections show discount message
            element.textContent = 'Neem deel en ontvang een kortingscode';
        }
    });

    const urgencyElements = document.querySelectorAll('[data-demo-urgency]');
    urgencyElements.forEach(element => {
        // Skip hero urgency elements - they should keep their original text
        if (element.closest('.btn-primary-hero') || element.closest('.hero-cta-buttons') || element.closest('[data-hero-urgency]')) {
            return;
        }

        element.textContent = 'Neem deel en ontvang een kortingscode';
        element.classList.add('text-green-400');
    });
}

// Date formatting functions
function formatDateTime(date) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
        return `Vandaag ${date.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
        return `Morgen ${date.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
        return date.toLocaleDateString('nl-NL', {
            weekday: 'long',
            day: 'numeric',
            month: 'long',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

function formatDateShort(date) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
        return `Vandaag ${date.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
        return `Morgen ${date.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
        return date.toLocaleDateString('nl-NL', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

function formatDateRelative(date) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
        return 'vandaag';
    } else if (date.toDateString() === tomorrow.toDateString()) {
        return 'morgen';
    } else {
        const daysDiff = Math.ceil((date - today) / (1000 * 60 * 60 * 24));
        if (daysDiff <= 7) {
            return `over ${daysDiff} dagen`;
        } else {
            return date.toLocaleDateString('nl-NL', { day: 'numeric', month: 'long' });
        }
    }
}

// Duplicate function removed - using the one defined at top of file

// Duplicate functions removed - using the ones defined at top of file

// Clear all field errors
function clearAllFieldErrors() {
    const errorMessages = elements.sampleForm.querySelectorAll('.error-message');
    errorMessages.forEach(msg => msg.remove());

    const errorFields = elements.sampleForm.querySelectorAll('.border-red-500');
    errorFields.forEach(field => field.classList.remove('border-red-500'));
}

// Handle sample form submission
async function handleSampleFormSubmit(event) {
    event.preventDefault();
    console.log('📝 Submitting sample form...');

    // Validate form
    if (!validateForm()) {
        console.log('❌ Form validation failed');
        return;
    }

    // Show loading state
    setSubmitLoading(true);

    try {
        // Prepare form data
        const formData = new FormData(elements.sampleForm);
        const payload = createWebhookPayload(formData);

        // Submit to Resend (with webhook fallback)
        const response = await submitToResend(payload);

        if (response.success) {
            console.log('✅ Sample request submitted successfully');
            showSuccessMessage();
            trackEvent('form_submit', {
                form_type: 'sample_request',
                success: true
            });
        } else {
            throw new Error(response.error || 'Submission failed');
        }

    } catch (error) {
        console.error('❌ Sample form submission error:', error);
        showErrorMessage(error.message);
        trackEvent('form_submit', {
            form_type: 'sample_request',
            success: false,
            error: error.message
        });
    } finally {
        setSubmitLoading(false);
    }
}

// Validate entire form
function validateForm() {
    const requiredFields = elements.sampleForm.querySelectorAll('input[required], select[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });

    // Check GDPR consent
    const gdprConsent = document.getElementById('gdprConsent');
    if (!gdprConsent.checked) {
        showFieldError(gdprConsent, 'U moet akkoord gaan met het privacybeleid');
        isValid = false;
    }

    return isValid;
}

// Create webhook payload
function createWebhookPayload(formData) {
    return {
        timestamp: new Date().toISOString(),
        source: "nila_landing_samples",
        contact: {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            company: formData.get('company') || '',
            email: formData.get('email'),
            phone: formData.get('phone') || '',
            address: {
                street: formData.get('street'),
                city: formData.get('city'),
                postalCode: formData.get('postalCode'),
                country: formData.get('country')
            }
        },
        projectDetails: formData.get('projectDetails') || ''
    };
}

// Submit to Resend API
async function submitToResend(payload) {
    if (!CONFIG.resend.enabled || !CONFIG.resend.apiKey) {
        console.log('⚠️ Resend not configured, falling back to webhook');
        return submitToWebhook(payload);
    }

    try {
        const emailHtml = createEmailTemplate(payload);

        const response = await fetch('https://api.resend.com/emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.resend.apiKey}`
            },
            body: JSON.stringify({
                from: CONFIG.resend.fromEmail,
                to: [CONFIG.resend.toEmail],
                subject: `🎨 Nieuwe Sample Aanvraag - ${payload.contact.firstName} ${payload.contact.lastName}`,
                html: emailHtml,
                text: createEmailText(payload)
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Resend API Error: ${response.status} - ${errorData.message || response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Email sent via Resend:', result.id);
        return { success: true, id: result.id };

    } catch (error) {
        console.error('❌ Resend error:', error);
        // Fallback to webhook if Resend fails
        console.log('🔄 Falling back to webhook...');
        return submitToWebhook(payload);
    }
}

// Submit to webhook with retry logic (fallback)
async function submitToWebhook(payload, retryCount = 0) {
    const maxRetries = CONFIG.webhook.retryAttempts;

    try {
        const response = await fetch(CONFIG.webhook.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${CONFIG.webhook.apiKey}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        if (retryCount < maxRetries) {
            console.log(`🔄 Retrying webhook submission (${retryCount + 1}/${maxRetries})...`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.webhook.retryDelay * (retryCount + 1)));
            return submitToWebhook(payload, retryCount + 1);
        }
        throw error;
    }
}

// Set submit button loading state
function setSubmitLoading(isLoading) {
    if (isLoading) {
        elements.submitText.classList.add('hidden');
        elements.submitLoading.classList.remove('hidden');
        elements.submitButton.disabled = true;
    } else {
        elements.submitText.classList.remove('hidden');
        elements.submitLoading.classList.add('hidden');
        elements.submitButton.disabled = false;
    }
}

// Show success message
function showSuccessMessage() {
    // Replace form content with success message
    const formContainer = elements.sampleForm.parentNode;
    formContainer.innerHTML = `
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-nila-navy mb-2">Bedankt voor uw aanvraag!</h3>
            <p class="text-nila-dark-gray mb-6">
                Uw sample aanvraag is succesvol verzonden. U ontvangt binnen 24 uur een bevestiging
                per e-mail en uw samples worden binnen 3-5 werkdagen verzonden.
            </p>
            <button onclick="closeSampleForm()" class="btn-primary">
                Sluiten
            </button>
        </div>
    `;

    // Auto-close after 5 seconds
    setTimeout(() => {
        closeSampleForm();
    }, 5000);
}

// Show error message
function showErrorMessage(errorMessage) {
    // Show error at top of form
    const errorDiv = document.createElement('div');
    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
    errorDiv.innerHTML = `
        <strong>Er is een fout opgetreden:</strong> ${errorMessage}
        <br><small>Probeer het opnieuw of neem contact met ons op.</small>
    `;

    elements.sampleForm.insertBefore(errorDiv, elements.sampleForm.firstChild);

    // Remove error after 10 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 10000);
}

// Analytics initialization
function initializeAnalytics() {
    // Google Analytics 4
    if (CONFIG.analytics.googleAnalytics.enabled && CONFIG.analytics.googleAnalytics.measurementId) {
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', CONFIG.analytics.googleAnalytics.measurementId);
        console.log('📊 Google Analytics initialized');
    }

    // Facebook Pixel
    if (CONFIG.analytics.facebookPixel.enabled && CONFIG.analytics.facebookPixel.pixelId) {
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', CONFIG.analytics.facebookPixel.pixelId);
        fbq('track', 'PageView');
        console.log('📊 Facebook Pixel initialized');
    }

    // Hotjar (optional)
    if (CONFIG.analytics.hotjar.enabled && CONFIG.analytics.hotjar.siteId) {
        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:CONFIG.analytics.hotjar.siteId,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        console.log('📊 Hotjar initialized');
    }
}

// Track events
function trackEvent(eventName, parameters = {}) {
    console.log(`📊 Tracking event: ${eventName}`, parameters);

    // Google Analytics
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }

    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', eventName, parameters);
    }
}

// Utility: Smooth scroll to element
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Legacy Calendly functions removed - now using Cal.com integration

// updateDemonstrationTimes now handled by Cal.com integration

// All Calendly functions removed - using Cal.com integration instead

// Test function for sticky CTA bar
window.testStickyBar = function() {
    console.log('🧪 Testing sticky CTA bar...');
    const stickyBar = document.getElementById('sticky-cta-bar');
    if (stickyBar) {
        console.log('✅ Sticky bar found:', stickyBar);
        console.log('Current classes:', stickyBar.className);
        console.log('Current style:', stickyBar.style.cssText);
        console.log('Computed style display:', window.getComputedStyle(stickyBar).display);
        console.log('Computed style transform:', window.getComputedStyle(stickyBar).transform);
        console.log('Screen width:', window.innerWidth);
        console.log('Is desktop?', window.innerWidth >= 769);

        // Force show
        stickyBar.classList.remove('hidden');
        stickyBar.style.transform = 'translateY(0)';
        stickyBar.style.display = 'block';
        console.log('✅ Sticky bar forced to show');

        // Check if it's actually visible now
        setTimeout(() => {
            const rect = stickyBar.getBoundingClientRect();
            console.log('Element position:', rect);
            console.log('Is visible?', rect.height > 0 && rect.width > 0);
        }, 100);

        return { success: true, element: stickyBar };
    } else {
        console.error('❌ Sticky bar not found');
        return { success: false, error: 'Element not found' };
    }
};

// Force show sticky bar (for testing)
window.showStickyBar = function() {
    const stickyBar = document.getElementById('sticky-cta-bar');
    if (stickyBar) {
        stickyBar.classList.remove('hidden');
        stickyBar.style.transform = 'translateY(0)';
        stickyBar.style.display = 'block';
        console.log('✅ Sticky bar force shown');
        return true;
    }
    return false;
};

// Test scroll trigger
window.testScrollTrigger = function() {
    console.log('🧪 Testing scroll trigger...');
    console.log('Current scroll position:', window.scrollY);
    console.log('Is mobile?', window.innerWidth < 769);
    console.log('Trigger point:', window.innerWidth < 769 ? 200 : 300);
    console.log('Should show?', window.scrollY > (window.innerWidth < 769 ? 200 : 300));

    // Simulate scroll to trigger point
    window.scrollTo(0, window.innerWidth < 769 ? 250 : 350);
    console.log('✅ Scrolled to trigger point');
};

// Debug scroll listener
window.debugScrollListener = function() {
    console.log('🔍 Debugging scroll listener...');

    // Check if element exists
    const stickyBar = document.getElementById('sticky-cta-bar');
    console.log('Element exists?', !!stickyBar);

    if (stickyBar) {
        console.log('Element classes:', stickyBar.className);
        console.log('Element style:', stickyBar.style.cssText);
        console.log('Computed display:', window.getComputedStyle(stickyBar).display);
    }

    // Manually trigger the scroll function
    console.log('🔄 Manually triggering scroll function...');

    // Scroll a bit and check
    window.scrollTo(0, 400);

    setTimeout(() => {
        console.log('After scroll - position:', window.scrollY);
        if (stickyBar) {
            console.log('After scroll - classes:', stickyBar.className);
            console.log('After scroll - transform:', stickyBar.style.transform);
            console.log('After scroll - display style:', stickyBar.style.display);
            console.log('After scroll - computed display:', window.getComputedStyle(stickyBar).display);
        }
    }, 500);
};

// Force trigger the sticky bar logic
window.forceTriggerSticky = function() {
    console.log('🔧 Force triggering sticky bar logic...');

    const stickyBar = document.getElementById('sticky-cta-bar');
    if (stickyBar) {
        console.log('Before force trigger:');
        console.log('- Classes:', stickyBar.className);
        console.log('- Style:', stickyBar.style.cssText);
        console.log('- Computed display:', window.getComputedStyle(stickyBar).display);

        // Force show with all overrides
        stickyBar.classList.remove('hidden');
        stickyBar.classList.remove('translate-y-full'); // Remove the transform class too!
        stickyBar.style.transform = 'translateY(0)';
        stickyBar.style.display = 'block';
        stickyBar.style.visibility = 'visible';
        stickyBar.style.opacity = '1';

        console.log('After force trigger:');
        console.log('- Classes:', stickyBar.className);
        console.log('- Style:', stickyBar.style.cssText);
        console.log('- Computed display:', window.getComputedStyle(stickyBar).display);

        return true;
    }
    return false;
};

// Export functions for global access
window.NILA = {
    openGroupDemo: window.openGroupDemo,
    openSampleForm: window.openSampleForm,
    closeSampleForm: window.closeSampleForm,
    trackEvent,
    scrollToElement,
    testCalcom: window.testCalcom,
    testStickyBar: window.testStickyBar,
    showStickyBar: window.showStickyBar,
    testScrollTrigger: window.testScrollTrigger,
    debugScrollListener: window.debugScrollListener,
    forceTriggerSticky: window.forceTriggerSticky
};

// Test functions
window.testCalcom = function() {
    console.log('🧪 Testing Cal.com...');
    console.log('🔍 Cal available?', typeof Cal !== 'undefined');
    console.log('🔍 Window.calcomLoaded?', window.calcomLoaded);
    console.log('🔍 Cal object:', typeof Cal !== 'undefined' ? Cal : 'undefined');

    if (typeof Cal !== 'undefined') {
        console.log('✅ Cal.com script loaded');
        try {
            Cal("init", { origin: "https://app.cal.com" });
            Cal("openModal", { calLink: "nila-poetry-of-light/nila-demo" });
            return { success: true, message: 'Cal.com working!' };
        } catch (error) {
            console.error('❌ Error:', error);
            return { success: false, error: error.message };
        }
    } else {
        console.error('❌ Cal.com script not loaded');
        return { success: false, error: 'Cal.com script not loaded' };
    }
};

// Debug function to check what's happening
window.debugCalcom = function() {
    console.log('🔍 Debug Cal.com state:');
    console.log('- typeof Cal:', typeof Cal);
    console.log('- window.calcomLoaded:', window.calcomLoaded);
    console.log('- Cal object:', Cal);

    // Check if script tag exists
    const scripts = document.querySelectorAll('script[src*="cal.com"]');
    console.log('- Cal.com script tags found:', scripts.length);
    scripts.forEach((script, index) => {
        console.log(`  Script ${index + 1}:`, script.src, 'loaded:', script.readyState);
    });

    return {
        calAvailable: typeof Cal !== 'undefined',
        calcomLoaded: window.calcomLoaded,
        scriptTags: scripts.length
    };
};

// Create HTML email template for NILA sample requests
function createEmailTemplate(payload) {
    const { contact, projectDetails } = payload;

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NILA Sample Aanvraag</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #373534; margin: 0; padding: 0; background-color: #F8F4EC; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #373534 0%, #DCC8B6 100%); color: white; padding: 30px; text-align: center; }
            .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
            .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }
            .content { padding: 30px; }
            .section { margin-bottom: 25px; }
            .section h2 { color: #373534; font-size: 20px; margin-bottom: 15px; border-bottom: 2px solid #CFB5A7; padding-bottom: 8px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
            .info-item { background: #F8F4EC; padding: 15px; border-radius: 8px; border-left: 4px solid #CFB5A7; }
            .info-item strong { color: #373534; display: block; margin-bottom: 5px; }
            .address { background: #F8F4EC; padding: 15px; border-radius: 8px; margin-top: 10px; }
            .project-details { background: #F8F4EC; padding: 20px; border-radius: 8px; border: 1px solid #DCC8B6; }
            .footer { background: #373534; color: white; padding: 20px; text-align: center; font-size: 14px; }
            .footer a { color: #CFB5A7; text-decoration: none; }
            @media (max-width: 600px) {
                .info-grid { grid-template-columns: 1fr; }
                .content { padding: 20px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 NILA Sample Aanvraag</h1>
                <p>Poetry of Light - Nieuwe aanvraag ontvangen</p>
            </div>

            <div class="content">
                <div class="section">
                    <h2>👤 Contactgegevens</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Naam:</strong>
                            ${contact.firstName} ${contact.lastName}
                        </div>
                        <div class="info-item">
                            <strong>Email:</strong>
                            <a href="mailto:${contact.email}" style="color: #373534;">${contact.email}</a>
                        </div>
                        ${contact.phone ? `
                        <div class="info-item">
                            <strong>Telefoon:</strong>
                            <a href="tel:${contact.phone}" style="color: #373534;">${contact.phone}</a>
                        </div>
                        ` : ''}
                        ${contact.company ? `
                        <div class="info-item">
                            <strong>Bedrijf:</strong>
                            ${contact.company}
                        </div>
                        ` : ''}
                    </div>

                    <div class="address">
                        <strong>📍 Verzendadres:</strong><br>
                        ${contact.address.street}<br>
                        ${contact.address.postalCode} ${contact.address.city}<br>
                        ${contact.address.country}
                    </div>
                </div>

                ${projectDetails ? `
                <div class="section">
                    <h2>🏗️ Project Details</h2>
                    <div class="project-details">
                        ${projectDetails.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}

                <div class="section">
                    <h2>📊 Aanvraag Details</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Datum:</strong>
                            ${new Date(payload.timestamp).toLocaleDateString('nl-NL', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}
                        </div>
                        <div class="info-item">
                            <strong>Bron:</strong>
                            NILA Landing Page
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p><strong>NILA - Poetry of Light</strong></p>
                <p>Zijlweg 7, 5145 NR Waalwijk | <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><a href="https://wearenila.com">wearenila.com</a></p>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Create plain text version for accessibility
function createEmailText(payload) {
    const { contact, projectDetails } = payload;

    return `
NILA Sample Aanvraag - Poetry of Light

CONTACTGEGEVENS:
Naam: ${contact.firstName} ${contact.lastName}
Email: ${contact.email}
${contact.phone ? `Telefoon: ${contact.phone}` : ''}
${contact.company ? `Bedrijf: ${contact.company}` : ''}

VERZENDADRES:
${contact.address.street}
${contact.address.postalCode} ${contact.address.city}
${contact.address.country}

${projectDetails ? `PROJECT DETAILS:\n${projectDetails}\n` : ''}

AANVRAAG DETAILS:
Datum: ${new Date(payload.timestamp).toLocaleDateString('nl-NL', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}
Bron: NILA Landing Page

---
NILA - Poetry of Light
Zijlweg 7, 5145 NR Waalwijk
<EMAIL>
https://wearenila.com
    `.trim();
};
