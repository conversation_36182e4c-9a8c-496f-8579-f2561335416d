const u={calcom:{url:"https://cal.com/nila-poetry-of-light/nila-demo",apiToken:"cal_live_1dc5e2e1320cf007c1dff0e00e9d458a",baseUrl:"https://api.cal.com/v2",username:"nila-poetry-of-light",eventTypeSlug:"nila-demo"},analytics:{googleAnalytics:{enabled:!1,measurementId:null},facebookPixel:{enabled:!1,pixelId:null},hotjar:{enabled:!1,siteId:null}}};window.openGroupDemo=function(){console.log("🗓️ Opening Cal.com booking..."),typeof d=="function"&&d("cta_click",{cta_type:"group_demo",cta_location:"hero"});try{console.log("✅ Triggering Cal.com popup via data attributes...");const e=document.createElement("button");e.setAttribute("data-cal-link","nila-poetry-of-light/nila-demo"),e.setAttribute("data-cal-config",'{"layout":"month_view","theme":"light"}'),e.style.display="none",document.body.appendChild(e),e.click(),setTimeout(()=>{document.body.removeChild(e)},100),setTimeout(()=>{y()},500)}catch(e){console.error("❌ Cal.com error:",e),console.log("🔄 Falling back to direct URL..."),window.open("https://cal.com/nila-poetry-of-light/nila-demo","_blank")}};window.openSampleForm=function(){console.log("📦 Opening sample form..."),typeof d=="function"&&d("cta_click",{cta_type:"sample_request",cta_location:"hero"});const e=document.getElementById("sample-modal");if(e){e.classList.remove("hidden"),document.body.style.overflow="hidden";const t=e.querySelector("input");t&&setTimeout(()=>t.focus(),100)}};window.closeSampleForm=function(){console.log("❌ Closing sample form...");const e=document.getElementById("sample-modal");e&&(e.classList.add("hidden"),document.body.style.overflow="")};function y(){try{const e=document.querySelector("[data-cal-namespace]"),t=document.querySelector(".cal-modal-overlay, [data-cal-namespace] + div"),o=document.querySelector("[data-cal-namespace] iframe");if(e){const a=e.querySelector("div");a&&(a.style.background="#F8F4EC",a.style.borderRadius="1.5rem",a.style.overflow="hidden",a.style.boxShadow="0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3)",a.style.border="none",a.style.maxWidth="85vw",a.style.maxHeight="85vh")}t&&(t.style.background="rgba(55, 53, 52, 0.85)",t.style.backdropFilter="blur(12px)",t.style.webkitBackdropFilter="blur(12px)"),o&&(o.style.borderRadius="1.5rem",o.style.background="#F8F4EC",o.addEventListener("load",()=>{try{v(o)}catch{console.log("Cannot access iframe content (CORS)")}})),document.querySelectorAll('[data-cal-namespace] button[aria-label*="close"], [data-cal-namespace] .close, [data-cal-namespace] [data-testid="close"]').forEach(a=>{a.style.background="#F8F4EC",a.style.color="#373534",a.style.borderRadius="50%",a.style.width="44px",a.style.height="44px",a.style.border="2px solid #DCC8B6",a.style.transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",a.style.boxShadow="0 4px 12px rgba(55, 53, 52, 0.15)",a.style.position="absolute",a.style.top="1rem",a.style.right="1rem",a.style.zIndex="10",a.addEventListener("mouseenter",()=>{a.style.background="#CFB5A7",a.style.transform="scale(1.05)",a.style.borderColor="#CFB5A7",a.style.boxShadow="0 8px 20px rgba(207, 181, 167, 0.4)"}),a.addEventListener("mouseleave",()=>{a.style.background="#F8F4EC",a.style.transform="scale(1)",a.style.borderColor="#DCC8B6",a.style.boxShadow="0 4px 12px rgba(55, 53, 52, 0.15)"})}),console.log("✅ Enhanced Cal.com styles applied")}catch(e){console.error("❌ Error applying custom Cal.com styles:",e)}}function v(e){try{const t=e.contentDocument||e.contentWindow.document;if(!t)return;t.head.insertAdjacentHTML("beforeend",`
            <style>
                /* NILA Custom styles for Cal.com iframe content */
                body {
                    font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    background: #F8F4EC !important;
                }

                /* Header styling */
                h1, h2, h3 {
                    color: #373534 !important;
                    font-family: 'IBM Plex Sans', sans-serif !important;
                }

                /* Button styling */
                button[data-testid*="time"] {
                    border: 2px solid #DCC8B6 !important;
                    background: #F8F4EC !important;
                    color: #373534 !important;
                    border-radius: 0.75rem !important;
                    transition: all 0.3s ease !important;
                }

                button[data-testid*="time"]:hover {
                    background: #CFB5A7 !important;
                    border-color: #CFB5A7 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 4px 12px rgba(207, 181, 167, 0.3) !important;
                }

                /* Selected date styling */
                [data-testid*="day"][aria-selected="true"] {
                    background: #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                }

                /* Primary buttons */
                button[type="submit"], .btn-primary {
                    background: #CFB5A7 !important;
                    border: 2px solid #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                    font-weight: 600 !important;
                    transition: all 0.3s ease !important;
                }

                button[type="submit"]:hover, .btn-primary:hover {
                    background: #373534 !important;
                    border-color: #373534 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 8px 20px rgba(55, 53, 52, 0.3) !important;
                }
            </style>
        `),console.log("✅ Iframe styles injected")}catch{console.log("Cannot inject iframe styles (CORS restriction)")}}function k(){if(document.getElementById("nila-calcom-custom-css"))return;const e=`
        /* NILA Custom Cal.com Styling - Enhanced */
        [data-cal-namespace] {
            z-index: 9999 !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        /* Main popup container */
        [data-cal-namespace] > div {
            border-radius: 1.5rem !important;
            box-shadow: 0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3) !important;
            overflow: hidden !important;
            border: none !important;
            max-width: 85vw !important;
            max-height: 85vh !important;
            background: #F8F4EC !important;
        }

        /* Backdrop overlay */
        [data-cal-namespace] + div {
            background: rgba(55, 53, 52, 0.85) !important;
            backdrop-filter: blur(12px) !important;
            -webkit-backdrop-filter: blur(12px) !important;
        }

        /* Main iframe styling */
        [data-cal-namespace] iframe {
            border-radius: 1.5rem !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            background: #F8F4EC !important;
        }

        /* Close button styling */
        [data-cal-namespace] button[aria-label*="close"],
        [data-cal-namespace] .close,
        [data-cal-namespace] [data-testid="close"] {
            background: #F8F4EC !important;
            color: #373534 !important;
            border-radius: 50% !important;
            width: 44px !important;
            height: 44px !important;
            border: 2px solid #DCC8B6 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 4px 12px rgba(55, 53, 52, 0.15) !important;
            position: absolute !important;
            top: 1rem !important;
            right: 1rem !important;
            z-index: 10 !important;
        }

        [data-cal-namespace] button[aria-label*="close"]:hover,
        [data-cal-namespace] .close:hover,
        [data-cal-namespace] [data-testid="close"]:hover {
            background: #CFB5A7 !important;
            transform: scale(1.05) !important;
            border-color: #CFB5A7 !important;
            box-shadow: 0 8px 20px rgba(207, 181, 167, 0.4) !important;
        }

        /* Header styling inside iframe */
        [data-cal-namespace] iframe[src*="cal.com"] {
            /* Custom CSS will be injected into iframe content */
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            [data-cal-namespace] > div {
                margin: 0.75rem !important;
                max-width: calc(100vw - 1.5rem) !important;
                max-height: calc(100vh - 1.5rem) !important;
                border-radius: 1.25rem !important;
            }

            [data-cal-namespace] button[aria-label*="close"],
            [data-cal-namespace] .close,
            [data-cal-namespace] [data-testid="close"] {
                width: 40px !important;
                height: 40px !important;
                top: 0.75rem !important;
                right: 0.75rem !important;
            }
        }

        /* Animation for popup appearance */
        [data-cal-namespace] > div {
            animation: nilaPopupAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        @keyframes nilaPopupAppear {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Enhanced backdrop animation */
        [data-cal-namespace] + div {
            animation: nilaBackdropAppear 0.3s ease-out !important;
        }

        @keyframes nilaBackdropAppear {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
    `,t=document.createElement("style");t.id="nila-calcom-custom-css",t.textContent=e,document.head.appendChild(t),console.log("✅ Custom Cal.com CSS injected")}function x(){new MutationObserver(t=>{t.forEach(o=>{o.addedNodes.forEach(n=>{n.nodeType===Node.ELEMENT_NODE&&(n.hasAttribute&&n.hasAttribute("data-cal-namespace")&&(console.log("🎨 Cal.com popup detected, applying custom styles..."),setTimeout(()=>{y()},100)),n.querySelector&&n.querySelector("[data-cal-namespace]")&&(console.log("🎨 Cal.com popup detected in child, applying custom styles..."),setTimeout(()=>{y()},100)))})})}).observe(document.body,{childList:!0,subtree:!0}),console.log("👀 Cal.com style monitor initialized")}const i={sampleModal:document.getElementById("sample-modal"),sampleForm:document.getElementById("sample-form"),submitButton:document.getElementById("submit-sample-form"),submitText:document.getElementById("submit-text"),submitLoading:document.getElementById("submit-loading"),floatingCta:document.getElementById("floating-cta"),stickyCtaBar:document.getElementById("sticky-cta-bar")};console.log("🧪 Adding immediate scroll test...");window.addEventListener("scroll",function(){console.log("🔄 IMMEDIATE SCROLL TEST - Position:",window.scrollY)});console.log("✅ Immediate scroll test added");document.addEventListener("DOMContentLoaded",function(){E()});function E(){console.log("🎨 NILA Landing Page - Initializing..."),i.sampleModal&&(i.sampleModal.classList.add("hidden"),document.body.style.overflow=""),L(),F(),M(),q(),console.log("🚀 About to initialize sticky CTA bar..."),N(),console.log("✅ Sticky CTA bar initialization completed"),_(),z(),k(),x(),console.log("✅ NILA Landing Page - Ready!")}function L(){const e={threshold:.1,rootMargin:"0px 0px -50px 0px"},t=new IntersectionObserver(n=>{n.forEach(a=>{a.isIntersecting&&a.target.classList.add("visible")})},e);document.querySelectorAll(".animate-on-scroll").forEach(n=>{t.observe(n)}),T();const o=document.querySelector(".nederlands-vakmanschap-image");o&&new IntersectionObserver(a=>{a.forEach(l=>{l.isIntersecting?(console.log("Nederlands vakmanschap image is in view - applying zoom"),l.target.style.transform="scale(1.15)",l.target.style.objectPosition="33% 50%"):l.target.style.transform="scale(1.0)"})},{threshold:.2,rootMargin:"0px 0px -100px 0px"}).observe(o)}function T(){const e=document.querySelectorAll("[data-count-to]"),t=new IntersectionObserver(o=>{o.forEach(n=>{n.isIntersecting&&!n.target.classList.contains("counted")&&(n.target.classList.add("counted"),A(n.target))})},{threshold:.5,rootMargin:"0px 0px -100px 0px"});e.forEach(o=>{t.observe(o)})}function A(e){const t=parseInt(e.getAttribute("data-count-to")),o=e.getAttribute("data-count-suffix")||"",n=2e3,a=performance.now(),l=0;function c(s){const r=s-a,m=Math.min(r/n,1),f=1-Math.pow(1-m,4),b=Math.floor(l+(t-l)*f);e.textContent=b+o,m<1?requestAnimationFrame(c):e.textContent=t+o}requestAnimationFrame(c)}function F(){const e=i.sampleForm;if(!e)return;e.querySelectorAll("input[required], select[required]").forEach(o=>{o.addEventListener("blur",C),o.addEventListener("input",I)})}function C(e){const t=e.target,o=t.value.trim();return t.classList.remove("border-red-500"),t.hasAttribute("required")&&!o?(g(t,"Dit veld is verplicht"),!1):t.type==="email"&&o&&!B(o)?(g(t,"Voer een geldig e-mailadres in"),!1):t.name==="postalCode"&&o&&!D(o)?(g(t,"Voer een geldige postcode in"),!1):!0}function I(e){const t=e.target;t.classList.remove("border-red-500");const o=t.parentNode.querySelector(".error-message");o&&o.remove()}function g(e,t){e.classList.add("border-red-500");const o=e.parentNode.querySelector(".error-message");o&&o.remove();const n=document.createElement("div");n.className="error-message text-red-500 text-sm mt-1",n.textContent=t,e.parentNode.appendChild(n)}function B(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function D(e){return/^[1-9][0-9]{3}\s?[A-Za-z]{2}$/.test(e)}function M(){i.sampleForm&&i.sampleForm.addEventListener("submit",Y),i.sampleModal&&i.sampleModal.addEventListener("click",e=>{e.target===i.sampleModal&&closeSampleForm()}),document.addEventListener("keydown",e=>{e.key==="Escape"&&!i.sampleModal.classList.contains("hidden")&&closeSampleForm()})}function _(){const e=document.querySelector(".hero-image");if(!e)return;let t=!1;function o(){const a=window.pageYOffset,c=document.getElementById("hero").offsetHeight;if(a<c){const r=a*.5;e.style.transform=`scale(1.4) translateY(${r}px)`;const m=Math.max(.8,1-a/c*.2);e.style.opacity=m}t=!1}function n(){t||(requestAnimationFrame(o),t=!0)}window.addEventListener("scroll",n,{passive:!0})}function q(){if(!i.floatingCta)return;let e=!1;function t(){const a=window.scrollY,l=window.innerHeight;a>l&&!e?(i.floatingCta.classList.remove("hidden"),e=!0,d("floating_cta_shown",{scroll_position:a})):a<=l&&e&&(i.floatingCta.classList.add("hidden"),e=!1)}let o=!1;function n(){o||(requestAnimationFrame(()=>{t(),o=!1}),o=!0)}window.addEventListener("scroll",n),i.floatingCta.addEventListener("click",()=>{d("floating_cta_click",{cta_type:"floating_demo_button"})})}function N(){console.log("🔧 Initializing sticky CTA bar...");const e=document.getElementById("sticky-cta-bar")||i.stickyCtaBar;if(!e){console.error("❌ Sticky CTA bar element not found!"),console.log("Available elements:",Object.keys(i));return}console.log("✅ Sticky CTA bar element found:",e),i.stickyCtaBar=e;let t=!1,o=!1;function n(){const c=window.scrollY,s=window.innerWidth<769,r=s?200:300,m=document.documentElement.scrollHeight,f=window.innerHeight,p=c+f>m-200;console.log("🔄 SCROLL EVENT DETECTED:",{scrollPosition:c,triggerPoint:r,isVisible:t,nearBottom:p,isMobile:s,shouldShow:c>r&&!t&&!p,elementExists:!!i.stickyCtaBar}),c>r&&!t&&!p?(console.log("✅ Showing sticky CTA bar"),i.stickyCtaBar.classList.remove("hidden"),i.stickyCtaBar.classList.remove("translate-y-full"),i.stickyCtaBar.style.transform="translateY(0)",i.stickyCtaBar.style.display="block",t=!0,o||(d("sticky_cta_shown",{scroll_position:c,is_mobile:s,trigger_point:r}),o=!0)):(c<=r||p)&&t&&(console.log("❌ Hiding sticky CTA bar"),i.stickyCtaBar.classList.add("translate-y-full"),i.stickyCtaBar.style.transform="translateY(100%)",setTimeout(()=>{t||i.stickyCtaBar.classList.add("hidden")},300),t=!1)}let a=!1;function l(){a||(requestAnimationFrame(()=>{n(),a=!1}),a=!0)}window.addEventListener("scroll",l),console.log("✅ Sticky CTA bar scroll listener added"),window.addEventListener("scroll",()=>{console.log("🔄 Basic scroll event detected at:",window.scrollY)}),console.log("🧪 Initial scroll position:",window.scrollY),console.log("🧪 Initial trigger test..."),n()}window.hideStickyBar=function(){i.stickyCtaBar&&(i.stickyCtaBar.style.transform="translateY(100%)",setTimeout(()=>{i.stickyCtaBar.classList.add("hidden")},300),d("sticky_cta_dismissed",{user_action:!0}))};function z(){console.log("🗓️ Initializing Cal.com integration..."),typeof Cal<"u"?(Cal("init",{origin:"https://app.cal.com"}),Cal("ui",{styles:{branding:{brandColor:"#CFB5A7"}},hideEventTypeDetails:!1,layout:"month_view"}),console.log("✅ Cal.com initialized")):console.log("⚠️ Cal.com script not loaded yet"),h(),setInterval(h,10*60*1e3),console.log("✅ Cal.com integration initialized")}async function h(){console.log("🗓️ Updating demo times from Cal.com API...");try{const e=await fetch(`${u.calcom.baseUrl}/slots/available?username=${u.calcom.username}&eventTypeSlug=${u.calcom.eventTypeSlug}&startTime=${new Date().toISOString()}&endTime=${new Date(Date.now()+6048e5).toISOString()}`,{headers:{Authorization:`Bearer ${u.calcom.apiToken}`,"Content-Type":"application/json"}});if(e.ok){const t=await e.json();if(console.log("✅ Cal.com API response:",t),t.slots&&t.slots.length>0){const o=t.slots[0],n=new Date(o.time);document.querySelectorAll("[data-demo-time]").forEach(s=>{const r=s.getAttribute("data-demo-format");r==="week"?s.textContent=`Volgende demonstratie ${R(n)}`:r==="short"?s.textContent=$(n):s.textContent=O(n)}),document.querySelectorAll("[data-demo-availability]").forEach(s=>{var m;const r=o.invitees_remaining||o.max_invitees_per_event-(((m=o.invitees)==null?void 0:m.length)||0)||12;s.textContent=`Nog ${r} plaatsen beschikbaar`}),document.querySelectorAll("[data-demo-urgency]").forEach(s=>{const r=o.invitees_remaining||12;r<=3?(s.textContent=`Laatste ${r} plaatsen!`,s.classList.add("text-red-400")):r<=5?(s.textContent="Beperkte plaatsen beschikbaar",s.classList.add("text-yellow-400")):(s.textContent="Plaatsen beschikbaar",s.classList.add("text-green-400"))}),console.log("✅ Demo times updated from Cal.com API");return}}else console.warn("⚠️ Cal.com API error:",e.status,e.statusText)}catch(e){console.error("❌ Error fetching Cal.com data:",e)}console.log("🔄 Using fallback demo times..."),P()}function P(){const e=new Date;e.setDate(e.getDate()+1),e.setHours(14,0,0,0),document.querySelectorAll("[data-demo-time]").forEach(a=>{const l=a.getAttribute("data-demo-format");l==="week"?a.textContent="Volgende demonstratie morgen":a.textContent="Morgen 14:00"}),document.querySelectorAll("[data-demo-availability]").forEach(a=>{a.textContent="Nog 3 plaatsen beschikbaar"}),document.querySelectorAll("[data-demo-urgency]").forEach(a=>{a.textContent="Laatste plaatsen!",a.classList.add("text-red-400")})}function O(e){const t=new Date,o=new Date(t);return o.setDate(o.getDate()+1),e.toDateString()===t.toDateString()?`Vandaag ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toDateString()===o.toDateString()?`Morgen ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toLocaleDateString("nl-NL",{weekday:"long",day:"numeric",month:"long",hour:"2-digit",minute:"2-digit"})}function $(e){const t=new Date,o=new Date(t);return o.setDate(o.getDate()+1),e.toDateString()===t.toDateString()?`Vandaag ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toDateString()===o.toDateString()?`Morgen ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toLocaleDateString("nl-NL",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"})}function R(e){const t=new Date,o=new Date(t);if(o.setDate(o.getDate()+1),e.toDateString()===t.toDateString())return"vandaag";if(e.toDateString()===o.toDateString())return"morgen";{const n=Math.ceil((e-t)/864e5);return n<=7?`over ${n} dagen`:e.toLocaleDateString("nl-NL",{day:"numeric",month:"long"})}}async function Y(e){if(e.preventDefault(),console.log("📝 Submitting sample form..."),!j()){console.log("❌ Form validation failed");return}w(!0);try{const t=new FormData(i.sampleForm),o=H(t),n=await S(o);if(n.success)console.log("✅ Sample request submitted successfully"),V(),d("form_submit",{form_type:"sample_request",success:!0});else throw new Error(n.error||"Submission failed")}catch(t){console.error("❌ Sample form submission error:",t),W(t.message),d("form_submit",{form_type:"sample_request",success:!1,error:t.message})}finally{w(!1)}}function j(){const e=i.sampleForm.querySelectorAll("input[required], select[required]");let t=!0;e.forEach(n=>{C({target:n})||(t=!1)});const o=document.getElementById("gdprConsent");return o.checked||(g(o,"U moet akkoord gaan met het privacybeleid"),t=!1),t}function H(e){return{timestamp:new Date().toISOString(),source:"nila_landing_samples",contact:{firstName:e.get("firstName"),lastName:e.get("lastName"),company:e.get("company")||"",email:e.get("email"),phone:e.get("phone")||"",address:{street:e.get("street"),city:e.get("city"),postalCode:e.get("postalCode"),country:e.get("country")}},projectDetails:e.get("projectDetails")||""}}async function S(e,t=0){const o=u.webhook.retryAttempts;try{const n=await fetch(u.webhook.url,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${u.webhook.apiKey}`},body:JSON.stringify(e)});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);return await n.json()}catch(n){if(t<o)return console.log(`🔄 Retrying webhook submission (${t+1}/${o})...`),await new Promise(a=>setTimeout(a,u.webhook.retryDelay*(t+1))),S(e,t+1);throw n}}function w(e){e?(i.submitText.classList.add("hidden"),i.submitLoading.classList.remove("hidden"),i.submitButton.disabled=!0):(i.submitText.classList.remove("hidden"),i.submitLoading.classList.add("hidden"),i.submitButton.disabled=!1)}function V(){const e=i.sampleForm.parentNode;e.innerHTML=`
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-nila-navy mb-2">Bedankt voor uw aanvraag!</h3>
            <p class="text-nila-dark-gray mb-6">
                Uw sample aanvraag is succesvol verzonden. U ontvangt binnen 24 uur een bevestiging
                per e-mail en uw samples worden binnen 3-5 werkdagen verzonden.
            </p>
            <button onclick="closeSampleForm()" class="btn-primary">
                Sluiten
            </button>
        </div>
    `,setTimeout(()=>{closeSampleForm()},5e3)}function W(e){const t=document.createElement("div");t.className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",t.innerHTML=`
        <strong>Er is een fout opgetreden:</strong> ${e}
        <br><small>Probeer het opnieuw of neem contact met ons op.</small>
    `,i.sampleForm.insertBefore(t,i.sampleForm.firstChild),setTimeout(()=>{t.parentNode&&t.remove()},1e4)}function d(e,t={}){console.log(`📊 Tracking event: ${e}`,t),typeof gtag<"u"&&gtag("event",e,t),typeof fbq<"u"&&fbq("track",e,t)}function U(e){const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})}window.testStickyBar=function(){console.log("🧪 Testing sticky CTA bar...");const e=document.getElementById("sticky-cta-bar");return e?(console.log("✅ Sticky bar found:",e),console.log("Current classes:",e.className),console.log("Current style:",e.style.cssText),console.log("Computed style display:",window.getComputedStyle(e).display),console.log("Computed style transform:",window.getComputedStyle(e).transform),console.log("Screen width:",window.innerWidth),console.log("Is desktop?",window.innerWidth>=769),e.classList.remove("hidden"),e.style.transform="translateY(0)",e.style.display="block",console.log("✅ Sticky bar forced to show"),setTimeout(()=>{const t=e.getBoundingClientRect();console.log("Element position:",t),console.log("Is visible?",t.height>0&&t.width>0)},100),{success:!0,element:e}):(console.error("❌ Sticky bar not found"),{success:!1,error:"Element not found"})};window.showStickyBar=function(){const e=document.getElementById("sticky-cta-bar");return e?(e.classList.remove("hidden"),e.style.transform="translateY(0)",e.style.display="block",console.log("✅ Sticky bar force shown"),!0):!1};window.testScrollTrigger=function(){console.log("🧪 Testing scroll trigger..."),console.log("Current scroll position:",window.scrollY),console.log("Is mobile?",window.innerWidth<769),console.log("Trigger point:",window.innerWidth<769?200:300),console.log("Should show?",window.scrollY>(window.innerWidth<769?200:300)),window.scrollTo(0,window.innerWidth<769?250:350),console.log("✅ Scrolled to trigger point")};window.debugScrollListener=function(){console.log("🔍 Debugging scroll listener...");const e=document.getElementById("sticky-cta-bar");console.log("Element exists?",!!e),e&&(console.log("Element classes:",e.className),console.log("Element style:",e.style.cssText),console.log("Computed display:",window.getComputedStyle(e).display)),console.log("🔄 Manually triggering scroll function..."),window.scrollTo(0,400),setTimeout(()=>{console.log("After scroll - position:",window.scrollY),e&&(console.log("After scroll - classes:",e.className),console.log("After scroll - transform:",e.style.transform),console.log("After scroll - display style:",e.style.display),console.log("After scroll - computed display:",window.getComputedStyle(e).display))},500)};window.forceTriggerSticky=function(){console.log("🔧 Force triggering sticky bar logic...");const e=document.getElementById("sticky-cta-bar");return e?(console.log("Before force trigger:"),console.log("- Classes:",e.className),console.log("- Style:",e.style.cssText),console.log("- Computed display:",window.getComputedStyle(e).display),e.classList.remove("hidden"),e.classList.remove("translate-y-full"),e.style.transform="translateY(0)",e.style.display="block",e.style.visibility="visible",e.style.opacity="1",console.log("After force trigger:"),console.log("- Classes:",e.className),console.log("- Style:",e.style.cssText),console.log("- Computed display:",window.getComputedStyle(e).display),!0):!1};window.NILA={openGroupDemo:window.openGroupDemo,openSampleForm:window.openSampleForm,closeSampleForm:window.closeSampleForm,trackEvent:d,scrollToElement:U,testCalcom:window.testCalcom,testStickyBar:window.testStickyBar,showStickyBar:window.showStickyBar,testScrollTrigger:window.testScrollTrigger,debugScrollListener:window.debugScrollListener,forceTriggerSticky:window.forceTriggerSticky};window.testCalcom=function(){if(console.log("🧪 Testing Cal.com..."),console.log("🔍 Cal available?",typeof Cal<"u"),console.log("🔍 Window.calcomLoaded?",window.calcomLoaded),console.log("🔍 Cal object:",typeof Cal<"u"?Cal:"undefined"),typeof Cal<"u"){console.log("✅ Cal.com script loaded");try{return Cal("init",{origin:"https://app.cal.com"}),Cal("openModal",{calLink:"nila-poetry-of-light/nila-demo"}),{success:!0,message:"Cal.com working!"}}catch(e){return console.error("❌ Error:",e),{success:!1,error:e.message}}}else return console.error("❌ Cal.com script not loaded"),{success:!1,error:"Cal.com script not loaded"}};window.debugCalcom=function(){console.log("🔍 Debug Cal.com state:"),console.log("- typeof Cal:",typeof Cal),console.log("- window.calcomLoaded:",window.calcomLoaded),console.log("- Cal object:",Cal);const e=document.querySelectorAll('script[src*="cal.com"]');return console.log("- Cal.com script tags found:",e.length),e.forEach((t,o)=>{console.log(`  Script ${o+1}:`,t.src,"loaded:",t.readyState)}),{calAvailable:typeof Cal<"u",calcomLoaded:window.calcomLoaded,scriptTags:e.length}};
