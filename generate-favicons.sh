#!/bin/bash
# NILA Favicon Generator Script
# Vereist: ImageMagick (brew install imagemagick of apt-get install imagemagick)

echo "🎨 Generating NILA favicons from Logo_Nila_Zwart.jpg..."

# Controleer of ImageMagick is geïnstalleerd
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick is niet geïnstalleerd. Installeer het eerst:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    echo "   Windows: Download van https://imagemagick.org/script/download.php"
    exit 1
fi

# Controleer of het logo bestand bestaat
if [ ! -f "public/Logo_Nila_Zwart.jpg" ]; then
    echo "❌ Logo bestand niet gevonden: public/Logo_Nila_Zwart.jpg"
    exit 1
fi

cd public

echo "📐 Generating favicon.ico (16x16, 32x32, 48x48)..."
convert Logo_Nila_Zwart.jpg -resize 48x48 -background white -gravity center -extent 48x48 favicon-48.png
convert Logo_Nila_Zwart.jpg -resize 32x32 -background white -gravity center -extent 32x32 favicon-32x32.png
convert Logo_Nila_Zwart.jpg -resize 16x16 -background white -gravity center -extent 16x16 favicon-16x16.png
convert favicon-16x16.png favicon-32x32.png favicon-48.png favicon.ico

echo "🍎 Generating Apple Touch Icon (180x180)..."
convert Logo_Nila_Zwart.jpg -resize 180x180 -background white -gravity center -extent 180x180 apple-touch-icon.png

echo "🤖 Generating Android Chrome icons..."
convert Logo_Nila_Zwart.jpg -resize 192x192 -background transparent -gravity center -extent 192x192 android-chrome-192x192.png
convert Logo_Nila_Zwart.jpg -resize 512x512 -background transparent -gravity center -extent 512x512 android-chrome-512x512.png

echo "🧹 Cleaning up temporary files..."
rm -f favicon-48.png

echo "✅ Favicons generated successfully!"
echo "📁 Generated files:"
echo "   - favicon.ico"
echo "   - favicon-16x16.png"
echo "   - favicon-32x32.png"
echo "   - apple-touch-icon.png"
echo "   - android-chrome-192x192.png"
echo "   - android-chrome-512x512.png"
echo "   - site.webmanifest"

cd ..
echo "🚀 Ready for deployment!"
