# 📊 NILA Analytics Setup Guide

## 🎯 Overzicht

De NILA landing page heeft een uitgebreid analytics systeem dat automatisch belangrijke conversie events tracked. Dit document legt uit hoe je Google Analytics 4 en andere tracking tools configureert.

## 🚀 Quick Start

### 1. Google Analytics 4 Setup

**Stap 1: GA4 Property Aanmaken**
1. Ga naar [Google Analytics](https://analytics.google.com/)
2. Klik "Create Property" 
3. Vul in:
   - Property name: "NILA Landing Page"
   - Country: Netherlands
   - Currency: Euro (EUR)
4. <PERSON><PERSON><PERSON> je **Measurement ID** (begint met G-)

**Stap 2: Measurement ID Toevoegen**
Vervang in `index.html` regel 14:
```javascript
const GA4_ID = 'G-XXXXXXXXXX'; // VERVANG MET JOUW ECHTE GA4 ID
```

**Stap 3: Environment Variable (Optioneel)**
In `.env` bestand:
```
VITE_GA4_MEASUREMENT_ID=G-JOUW-ECHTE-ID
```

### 2. Conversie Events Configureren

In Google Analytics 4, ga naar **Events** → **Conversions** en markeer deze events als conversies:

#### 🎯 Primaire Conversies
- `demo_booking_started` - Demo booking gestart
- `demo_booking_completed` - Demo booking voltooid  
- `sample_request_completed` - Sample aanvraag voltooid
- `generate_lead` - Lead gegenereerd

#### 📈 Secundaire Events
- `sample_request_started` - Sample form geopend
- `view_promotion` - Sticky/floating CTA getoond
- `select_promotion` - CTA button geklikt

## 📊 Automatische Tracking

### Events die Automatisch Worden Getrackt

**Demo Booking Flow:**
```javascript
// Wanneer gebruiker op "Boek Demo" klikt
demo_booking_started {
    cta_type: 'group_demo',
    cta_location: 'hero',
    booking_platform: 'cal.com'
}

// Wanneer Cal.com booking wordt voltooid (handmatig te configureren)
demo_booking_completed {
    booking_value: 1,
    currency: 'EUR'
}
```

**Sample Request Flow:**
```javascript
// Wanneer sample form wordt geopend
sample_request_started {
    cta_type: 'sample_request',
    form_type: 'sample_modal'
}

// Wanneer sample form wordt verzonden
sample_request_completed {
    form_type: 'sample_request',
    success: true
}
```

**Engagement Events:**
```javascript
// Sticky CTA bar verschijnt
sticky_cta_shown {
    scroll_position: 340,
    is_mobile: false
}

// Floating CTA verschijnt  
floating_cta_shown {
    scroll_position: 800
}
```

## 🎨 Custom Dashboards

### Aanbevolen GA4 Rapporten

**1. Conversie Overzicht**
- Demo bookings per dag/week
- Sample requests per dag/week  
- Conversie rate per traffic source

**2. User Journey**
- Scroll depth analysis
- CTA engagement rates
- Form abandonment rates

**3. Traffic Analysis**
- Top traffic sources
- Device/browser breakdown
- Geographic distribution

### Custom Metrics Instellen

In GA4 → **Configure** → **Custom Definitions**:

**Custom Metrics:**
- `cta_engagement_rate` - CTA click rate
- `form_completion_rate` - Form completion rate
- `demo_booking_value` - Demo booking waarde

**Custom Dimensions:**
- `cta_location` - Waar CTA werd geklikt
- `booking_platform` - Cal.com vs andere
- `form_type` - Type formulier

## 🔧 Geavanceerde Configuratie

### Facebook Pixel (Optioneel)

In `src/scripts/main.js` regel 35:
```javascript
facebookPixel: {
    enabled: true,
    pixelId: 'JOUW_FB_PIXEL_ID'
}
```

### Hotjar Heatmaps (Optioneel)

In `src/scripts/main.js` regel 39:
```javascript
hotjar: {
    enabled: true,
    siteId: 'JOUW_HOTJAR_ID'
}
```

## 🧪 Testing & Validatie

### 1. Real-time Testing

**Browser Console:**
```javascript
// Test event tracking
window.trackNILAEvent('test_event', {test: true});

// Check if GA4 is loaded
console.log(typeof gtag !== 'undefined' ? 'GA4 Loaded' : 'GA4 Not Loaded');
```

**GA4 Real-time Reports:**
1. Ga naar GA4 → **Reports** → **Realtime**
2. Open je landing page
3. Klik op CTA buttons
4. Controleer of events verschijnen

### 2. Debug Mode

Voeg toe aan GA4 config:
```javascript
gtag('config', 'G-JOUW-ID', {
    debug_mode: true // Alleen voor testing
});
```

## 📈 KPI's & Doelstellingen

### Primaire KPI's
- **Demo Booking Rate**: > 2%
- **Sample Request Rate**: > 5%  
- **Overall Conversion Rate**: > 7%

### Secundaire Metrics
- **Bounce Rate**: < 60%
- **Average Session Duration**: > 2 minuten
- **Pages per Session**: > 1.5

## 🚨 Troubleshooting

### GA4 Niet Geladen
1. Check browser console voor errors
2. Controleer of Measurement ID correct is
3. Test in incognito mode (ad blockers)

### Events Niet Zichtbaar
1. Wacht 24-48 uur voor volledige data
2. Check Real-time reports voor directe feedback
3. Controleer event parameters in debug mode

### GDPR Compliance
- Cookie consent is geïmplementeerd
- Analytics cookies worden alleen geladen na consent
- Data retention ingesteld op 14 maanden

## 📞 Support

Voor vragen over analytics setup:
- Check browser console logs (F12)
- Test events in GA4 Real-time reports
- Gebruik debug mode voor troubleshooting

---

**✅ Analytics Status:** Geïmplementeerd en klaar voor gebruik
**🔧 Configuratie Vereist:** GA4 Measurement ID toevoegen
**📊 Tracking:** 8+ automatische events geconfigureerd
