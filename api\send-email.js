// Vercel Serverless Function for sending emails via Resend
export default async function handler(req, res) {
    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    try {
        const { contact, projectDetails, timestamp } = req.body;

        // Validate required fields
        if (!contact || !contact.firstName || !contact.email) {
            return res.status(400).json({ 
                error: 'Missing required contact information' 
            });
        }

        // Get Resend API key from environment
        const resendApiKey = process.env.RESEND_API_KEY;
        if (!resendApiKey) {
            console.error('❌ RESEND_API_KEY not found in environment variables');
            return res.status(500).json({ 
                error: 'Email service configuration error' 
            });
        }

        // Create email content
        const emailHtml = createEmailTemplate({ contact, projectDetails, timestamp });
        const emailText = createEmailText({ contact, projectDetails, timestamp });

        // Send email via Resend
        const response = await fetch('https://api.resend.com/emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${resendApiKey}`
            },
            body: JSON.stringify({
                from: 'Luna - NILA <<EMAIL>>',
                to: ['<EMAIL>'],
                subject: `🎨 Nieuwe Sample Aanvraag - ${contact.firstName} ${contact.lastName}`,
                html: emailHtml,
                text: emailText
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.error('❌ Resend API Error:', response.status, errorData);
            return res.status(500).json({ 
                error: 'Failed to send email',
                details: errorData 
            });
        }

        const result = await response.json();
        console.log('✅ Email sent successfully:', result.id);

        return res.status(200).json({ 
            success: true, 
            id: result.id,
            message: 'Sample aanvraag succesvol verstuurd naar Luna!'
        });

    } catch (error) {
        console.error('❌ Server error:', error);
        return res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}

// Create HTML email template
function createEmailTemplate({ contact, projectDetails, timestamp }) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NILA Sample Aanvraag</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #ffffff; }
            .container { max-width: 650px; margin: 0 auto; background: white; border: 1px solid #e0e0e0; }
            .header { background: #373534; color: white; padding: 25px 30px; }
            .header h1 { margin: 0; font-size: 22px; font-weight: 600; }
            .header p { margin: 8px 0 0 0; font-size: 14px; }
            .content { padding: 35px 30px; }
            .intro { margin-bottom: 30px; font-size: 16px; color: #333333; }
            .section { margin-bottom: 30px; }
            .section-title { color: #333333; font-size: 16px; font-weight: 600; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 1px solid #cccccc; }
            .info-table { width: 100%; border-collapse: collapse; }
            .info-table td { padding: 8px 0; vertical-align: top; }
            .info-table td:first-child { font-weight: 600; color: #333333; width: 160px; }
            .info-table td:last-child { color: #555555; }
            .address-block { padding: 15px 0; margin-top: 10px; }
            .address-block strong { color: #333333; }
            .project-details { padding: 15px 0; font-style: italic; color: #555555; }
            .footer { background: #f8f8f8; color: #666666; padding: 25px 30px; font-size: 13px; border-top: 1px solid #e0e0e0; }
            .footer p { margin: 5px 0; }
            .footer a { color: #333333; text-decoration: none; }
            .footer .company { font-weight: 600; font-size: 14px; margin-bottom: 10px; color: #333333; }
            .footer .sender { color: #999999; margin-top: 15px; font-size: 11px; }
            @media (max-width: 600px) {
                .content { padding: 25px 20px; }
                .header { padding: 20px; }
                .info-table td:first-child { width: 140px; font-size: 14px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Nieuwe Sample Aanvraag</h1>
                <p>NILA - Poetry of Light</p>
            </div>

            <div class="content">
                <div class="intro">
                    Beste Luna,<br><br>
                    Hierbij ontvangt u een nieuwe sample aanvraag via de NILA landing page. Onderstaand vindt u alle benodigde gegevens:
                </div>

                <div class="section">
                    <div class="section-title">Klantgegevens</div>
                    <table class="info-table">
                        <tr>
                            <td>Naam:</td>
                            <td>${contact.firstName} ${contact.lastName}</td>
                        </tr>
                        ${contact.company ? `
                        <tr>
                            <td>Bedrijf:</td>
                            <td>${contact.company}</td>
                        </tr>
                        ` : ''}
                    </table>
                </div>

                <div class="section">
                    <div class="section-title">Adresgegevens</div>
                    <div class="address-block">
                        <strong>Verzendadres:</strong><br>
                        ${contact.address.street}<br>
                        ${contact.address.postalCode} ${contact.address.city}<br>
                        ${contact.address.country}
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Contactgegevens</div>
                    <table class="info-table">
                        <tr>
                            <td>E-mailadres:</td>
                            <td><a href="mailto:${contact.email}" style="color: #373534; text-decoration: none;">${contact.email}</a></td>
                        </tr>
                        ${contact.phone ? `
                        <tr>
                            <td>Telefoonnummer:</td>
                            <td><a href="tel:${contact.phone}" style="color: #373534; text-decoration: none;">${contact.phone}</a></td>
                        </tr>
                        ` : ''}
                    </table>
                </div>

                ${projectDetails ? `
                <div class="section">
                    <div class="section-title">Projectomschrijving</div>
                    <div class="project-details">
                        "${projectDetails}"
                    </div>
                </div>
                ` : ''}

                <div class="section">
                    <div class="section-title">Aanvraagdetails</div>
                    <table class="info-table">
                        <tr>
                            <td>Datum & tijd:</td>
                            <td>${new Date(timestamp).toLocaleDateString('nl-NL', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}</td>
                        </tr>
                        <tr>
                            <td>Bron:</td>
                            <td>NILA Landing Page (discovernila.com)</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="footer">
                <div class="company">NILA - Poetry of Light</div>
                <p>Zijlweg 7, 5145 NR Waalwijk</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><a href="https://wearenila.com">wearenila.com</a></p>
                <div class="sender">Dit bericht is automatisch <NAME_EMAIL></div>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Create plain text version
function createEmailText({ contact, projectDetails, timestamp }) {
    return `
NIEUWE SAMPLE AANVRAAG - NILA Poetry of Light

Beste Luna,

Hierbij ontvangt u een nieuwe sample aanvraag via de NILA landing page.

KLANTGEGEVENS:
Naam: ${contact.firstName} ${contact.lastName}
${contact.company ? `Bedrijf: ${contact.company}` : ''}

ADRESGEGEVENS:
Verzendadres:
${contact.address.street}
${contact.address.postalCode} ${contact.address.city}
${contact.address.country}

CONTACTGEGEVENS:
E-mailadres: ${contact.email}
${contact.phone ? `Telefoonnummer: ${contact.phone}` : ''}

${projectDetails ? `PROJECTOMSCHRIJVING:\n"${projectDetails}"\n` : ''}

AANVRAAGDETAILS:
Datum & tijd: ${new Date(timestamp).toLocaleDateString('nl-NL', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}
Bron: NILA Landing Page (discovernila.com)

---
NILA - Poetry of Light
Zijlweg 7, 5145 NR Waalwijk
<EMAIL>
wearenila.com

Dit bericht is automatisch <NAME_EMAIL>
    `.trim();
}
