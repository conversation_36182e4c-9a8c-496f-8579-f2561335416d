/* Placeholder images using CSS gradients until real images are available */

.hero-image-placeholder {
  background: linear-gradient(
    135deg,
    #1E2A5E 0%,
    #2C3E7A 25%,
    #B8860B 50%,
    #D4AF37 75%,
    #FAF8F5 100%
  );
  background-size: 400% 400%;
  animation: gradientShift 8s ease-in-out infinite;
}

.product-image-placeholder {
  background: linear-gradient(
    45deg,
    #FAF8F5 0%,
    #D4AF37 50%,
    #B8860B 100%
  );
  background-size: 200% 200%;
  animation: productGlow 6s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes productGlow {
  0%, 100% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
}
