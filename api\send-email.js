// Vercel Serverless Function for sending emails via Resend
export default async function handler(req, res) {
    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    try {
        const { contact, projectDetails, timestamp } = req.body;

        // Validate required fields
        if (!contact || !contact.firstName || !contact.email) {
            return res.status(400).json({ 
                error: 'Missing required contact information' 
            });
        }

        // Get Resend API key from environment
        const resendApiKey = process.env.RESEND_API_KEY;
        if (!resendApiKey) {
            console.error('❌ RESEND_API_KEY not found in environment variables');
            return res.status(500).json({ 
                error: 'Email service configuration error' 
            });
        }

        // Create email content
        const emailHtml = createEmailTemplate({ contact, projectDetails, timestamp });
        const emailText = createEmailText({ contact, projectDetails, timestamp });

        // Send email via Resend
        const response = await fetch('https://api.resend.com/emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${resendApiKey}`
            },
            body: JSON.stringify({
                from: 'Luna - NILA <<EMAIL>>',
                to: ['<EMAIL>'],
                subject: `🎨 Nieuwe Sample Aanvraag - ${contact.firstName} ${contact.lastName}`,
                html: emailHtml,
                text: emailText
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.error('❌ Resend API Error:', response.status, errorData);
            return res.status(500).json({ 
                error: 'Failed to send email',
                details: errorData 
            });
        }

        const result = await response.json();
        console.log('✅ Email sent successfully:', result.id);

        return res.status(200).json({ 
            success: true, 
            id: result.id,
            message: 'Sample aanvraag succesvol verstuurd naar Luna!'
        });

    } catch (error) {
        console.error('❌ Server error:', error);
        return res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}

// Create HTML email template
function createEmailTemplate({ contact, projectDetails, timestamp }) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>NILA Sample Aanvraag</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #373534; margin: 0; padding: 0; background-color: #F8F4EC; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #373534 0%, #DCC8B6 100%); color: white; padding: 30px; text-align: center; }
            .header h1 { margin: 0; font-size: 28px; font-weight: 600; }
            .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 16px; }
            .content { padding: 30px; }
            .section { margin-bottom: 25px; }
            .section h2 { color: #373534; font-size: 20px; margin-bottom: 15px; border-bottom: 2px solid #CFB5A7; padding-bottom: 8px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
            .info-item { background: #F8F4EC; padding: 15px; border-radius: 8px; border-left: 4px solid #CFB5A7; }
            .info-item strong { color: #373534; display: block; margin-bottom: 5px; }
            .address { background: #F8F4EC; padding: 15px; border-radius: 8px; margin-top: 10px; }
            .project-details { background: #F8F4EC; padding: 20px; border-radius: 8px; border: 1px solid #DCC8B6; }
            .footer { background: #373534; color: white; padding: 20px; text-align: center; font-size: 14px; }
            .footer a { color: #CFB5A7; text-decoration: none; }
            @media (max-width: 600px) {
                .info-grid { grid-template-columns: 1fr; }
                .content { padding: 20px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎨 NILA Sample Aanvraag</h1>
                <p>Poetry of Light - Nieuwe aanvraag ontvangen via landing page</p>
            </div>
            
            <div class="content">
                <div class="section">
                    <h2>👤 Contactgegevens</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Naam:</strong>
                            ${contact.firstName} ${contact.lastName}
                        </div>
                        <div class="info-item">
                            <strong>Email:</strong>
                            <a href="mailto:${contact.email}" style="color: #373534;">${contact.email}</a>
                        </div>
                        ${contact.phone ? `
                        <div class="info-item">
                            <strong>Telefoon:</strong>
                            <a href="tel:${contact.phone}" style="color: #373534;">${contact.phone}</a>
                        </div>
                        ` : ''}
                        ${contact.company ? `
                        <div class="info-item">
                            <strong>Bedrijf:</strong>
                            ${contact.company}
                        </div>
                        ` : ''}
                    </div>
                    
                    <div class="address">
                        <strong>📍 Verzendadres:</strong><br>
                        ${contact.address.street}<br>
                        ${contact.address.postalCode} ${contact.address.city}<br>
                        ${contact.address.country}
                    </div>
                </div>
                
                ${projectDetails ? `
                <div class="section">
                    <h2>🏗️ Project Details</h2>
                    <div class="project-details">
                        ${projectDetails.replace(/\n/g, '<br>')}
                    </div>
                </div>
                ` : ''}
                
                <div class="section">
                    <h2>📊 Aanvraag Details</h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Datum:</strong>
                            ${new Date(timestamp).toLocaleDateString('nl-NL', { 
                                weekday: 'long', 
                                year: 'numeric', 
                                month: 'long', 
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}
                        </div>
                        <div class="info-item">
                            <strong>Bron:</strong>
                            NILA Landing Page
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <p><strong>NILA - Poetry of Light</strong></p>
                <p>Zijlweg 7, 5145 NR Waalwijk</p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><a href="https://wearenila.com">wearenila.com</a></p>
                <p style="font-size: 12px; opacity: 0.7; margin-top: 10px;"><NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    `;
}

// Create plain text version
function createEmailText({ contact, projectDetails, timestamp }) {
    return `
NILA Sample Aanvraag - Poetry of Light

CONTACTGEGEVENS:
Naam: ${contact.firstName} ${contact.lastName}
Email: ${contact.email}
${contact.phone ? `Telefoon: ${contact.phone}` : ''}
${contact.company ? `Bedrijf: ${contact.company}` : ''}

VERZENDADRES:
${contact.address.street}
${contact.address.postalCode} ${contact.address.city}
${contact.address.country}

${projectDetails ? `PROJECT DETAILS:\n${projectDetails}\n` : ''}

AANVRAAG DETAILS:
Datum: ${new Date(timestamp).toLocaleDateString('nl-NL', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}
Bron: NILA Landing Page

---
NILA - Poetry of Light
Zijlweg 7, 5145 NR Waalwijk
<EMAIL>
https://wearenila.com

<NAME_EMAIL>
    `.trim();
}
