# NILA Landing Page - Configuratie Handleiding

Deze handleiding beschrijft hoe je de NILA Sales Landing Page configureert voor productie gebruik.

## 🔧 Vereiste Configuraties

### 1. Calendly Integratie

**Bestand**: `src/config/production.js`

```javascript
calendly: {
    url: 'https://calendly.com/jouw-calendly-account/nila-demo', // VERVANG DEZE URL
    utm: {
        utmCampaign: 'nila__landing',
        utmSource: 'website',
        utmMedium: 'cta'
    }
}
```

**Stappen**:
1. Log in op je Calendly account
2. Maak een nieuw event type aan voor NILA demonstraties
3. Kopieer de event URL
4. Vervang de URL in de configuratie
5. Test de integratie in development mode

### 2. n8n Webhook Configuratie

**Bestand**: `src/config/production.js`

```javascript
webhook: {
    url: 'https://jouw-n8n-instance.com/webhook/nila-samples', // VERVANG DEZE URL
    apiKey: 'jouw-api-key-hier', // VERVANG DEZE API KEY
    retryAttempts: 3,
    retryDelay: 1000
}
```

**Stappen**:
1. Stel een n8n workflow in voor sample aanvragen
2. Maak een webhook trigger aan
3. Configureer de webhook om emails te versturen
4. Kopieer de webhook URL en API key
5. Update de configuratie
6. Test de webhook met een test payload

**Voorbeeld n8n Workflow**:
```json
{
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "path": "nila-samples",
        "httpMethod": "POST"
      }
    },
    {
      "name": "Send Email",
      "type": "n8n-nodes-base.emailSend",
      "parameters": {
        "to": "<EMAIL>",
        "subject": "Nieuwe Sample Aanvraag - NILA",
        "text": "Nieuwe sample aanvraag ontvangen van {{$json.contact.firstName}} {{$json.contact.lastName}}"
      }
    }
  ]
}
```

### 3. Google Analytics 4

**Bestand**: `src/config/production.js`

```javascript
analytics: {
    googleAnalytics: {
        measurementId: 'G-XXXXXXXXXX', // VERVANG MET JOUW GA4 ID
        enabled: true
    }
}
```

**Stappen**:
1. Ga naar Google Analytics
2. Maak een nieuwe property aan voor de landing page
3. Kopieer het Measurement ID (begint met G-)
4. Vervang de ID in de configuratie
5. Configureer conversie events in GA4

**Aanbevolen Events**:
- `cta_click` - CTA button clicks
- `form_submit` - Sample form submissions
- `calendly_open` - Calendly popup opens

### 4. Facebook Pixel (Optioneel)

**Bestand**: `src/config/production.js`

```javascript
facebookPixel: {
    pixelId: '1234567890123456', // VERVANG MET JOUW PIXEL ID
    enabled: true
}
```

**Stappen**:
1. Ga naar Facebook Business Manager
2. Maak een nieuwe Pixel aan
3. Kopieer het Pixel ID
4. Vervang de ID in de configuratie
5. Configureer custom events in Facebook

### 5. Domain en SEO

**Bestand**: `src/config/production.js`

```javascript
seo: {
    siteName: 'NILA - Poetry of Light',
    siteUrl: 'https://landing.wearenila.com', // VERVANG MET JOUW DOMAIN
    defaultTitle: 'NILA - Poetry of Light | Exclusieve Groepsdemonstratie & Samples',
    defaultDescription: 'Ontdek NILA\'s unieke handgemaakte albast verlichting...',
    defaultImage: '/assets/images/nila-hero-og.jpg',
    twitterHandle: '@wearenila'
}
```

**Stappen**:
1. Registreer een subdomain (bijv. landing.wearenila.com)
2. Update de siteUrl in de configuratie
3. Configureer SSL certificaat
4. Update DNS instellingen
5. Test alle links en redirects

## 🖼️ Afbeeldingen Vervangen

### Vereiste Afbeeldingen

1. **Hero Afbeelding**: `src/assets/images/hero-image.webp`
   - Afmetingen: 1920x1080px (minimaal)
   - Formaat: WebP (met JPG fallback)
   - Bestand: PBF6367-1024x768.jpg (uit PRD)

2. **Product Afbeeldingen**: 
   - `src/assets/images/product-1.webp` (PBF6715-HDR-1024x768.jpg)
   - `src/assets/images/product-2.webp` (PBF6097-768x1024.jpg)
   - Afmetingen: 800x600px
   - Formaat: WebP met JPG fallback

3. **Logo's**:
   - `src/assets/images/nila-logo-white.svg` (voor hero sectie)
   - `public/favicon.svg` (favicon)
   - `public/favicon.png` (PNG fallback)

4. **Open Graph Afbeelding**:
   - `public/assets/images/nila-hero-og.jpg`
   - Afmetingen: 1200x630px
   - Formaat: JPG

### Afbeeldingen Optimaliseren

```bash
# Installeer imagemin voor optimalisatie
npm install -g imagemin-cli imagemin-webp imagemin-mozjpeg

# Converteer naar WebP
imagemin src/assets/images/*.jpg --out-dir=src/assets/images --plugin=webp

# Optimaliseer JPG fallbacks
imagemin src/assets/images/*.jpg --out-dir=src/assets/images --plugin=mozjpeg
```

## 📧 Email Configuratie

### Fallback Email Setup

**Bestand**: `src/config/production.js`

```javascript
email: {
    fallbackEmail: '<EMAIL>', // VERVANG MET JOUW EMAIL
    supportEmail: '<EMAIL>' // VERVANG MET SUPPORT EMAIL
}
```

### Email Templates

Configureer email templates in je n8n workflow:

**Bevestiging voor klant**:
```html
<h2>Bedankt voor uw sample aanvraag!</h2>
<p>Beste {{firstName}},</p>
<p>Uw aanvraag voor NILA samples is succesvol ontvangen. Wij zullen uw samples binnen 3-5 werkdagen verzenden naar:</p>
<address>
{{street}}<br>
{{postalCode}} {{city}}<br>
{{country}}
</address>
<p>Met vriendelijke groet,<br>Het NILA Team</p>
```

**Notificatie voor NILA team**:
```html
<h2>Nieuwe Sample Aanvraag</h2>
<p><strong>Naam:</strong> {{firstName}} {{lastName}}</p>
<p><strong>Bedrijf:</strong> {{company}}</p>
<p><strong>Email:</strong> {{email}}</p>
<p><strong>Telefoon:</strong> {{phone}}</p>
<p><strong>Adres:</strong> {{street}}, {{postalCode}} {{city}}, {{country}}</p>
<p><strong>Project Details:</strong> {{projectDetails}}</p>
```

## 🔒 Beveiliging

### HTTPS Configuratie

1. Verkrijg SSL certificaat (Let's Encrypt aanbevolen)
2. Configureer web server voor HTTPS redirect
3. Update alle URLs naar HTTPS
4. Test SSL configuratie met SSL Labs

### GDPR Compliance

1. **Privacy Policy**: Update link in footer
2. **Cookie Consent**: Implementeer cookie banner indien nodig
3. **Data Retention**: Configureer automatische data verwijdering
4. **Consent Logging**: Log consent timestamps

### Content Security Policy

Voeg CSP headers toe aan je web server:

```
Content-Security-Policy: default-src 'self'; 
script-src 'self' 'unsafe-inline' https://assets.calendly.com https://www.googletagmanager.com https://connect.facebook.net; 
style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://assets.calendly.com; 
font-src 'self' https://fonts.gstatic.com; 
img-src 'self' data: https:; 
connect-src 'self' https://api.calendly.com https://www.google-analytics.com;
```

## 🚀 Deployment

### Productie Deployment

```bash
# 1. Update configuratie
vim src/config/production.js

# 2. Test lokaal
npm run dev

# 3. Build voor productie
npm run build

# 4. Test productie build
npm run preview

# 5. Deploy met script
./deploy.sh

# 6. Upload naar server
rsync -avz dist/ user@server:/var/www/nila-landing/
```

### Web Server Configuratie

**Nginx Voorbeeld**:
```nginx
server {
    listen 443 ssl http2;
    server_name landing.wearenila.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    root /var/www/nila-landing;
    index index.html;
    
    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript image/svg+xml;
    
    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Fallback to index.html for SPA
    try_files $uri $uri/ /index.html;
}
```

## 📊 Monitoring

### Performance Monitoring

1. **Google PageSpeed Insights**: Test regelmatig
2. **GTmetrix**: Monitor loading times
3. **Uptime Monitoring**: Stel alerts in
4. **Analytics**: Monitor conversie rates

### Error Tracking

Optioneel: Configureer Sentry voor error tracking:

```javascript
// In src/config/production.js
errorHandling: {
    enableErrorReporting: true,
    sentryDsn: 'https://<EMAIL>/project-id',
    logLevel: 'error'
}
```

## ✅ Pre-Launch Checklist

- [ ] Calendly URL geconfigureerd en getest
- [ ] Webhook URL en API key ingesteld
- [ ] Google Analytics tracking ID toegevoegd
- [ ] Facebook Pixel ID geconfigureerd (indien van toepassing)
- [ ] Alle afbeeldingen vervangen door echte NILA afbeeldingen
- [ ] Domain en SSL certificaat geconfigureerd
- [ ] Email templates ingesteld in n8n
- [ ] Privacy policy en algemene voorwaarden links bijgewerkt
- [ ] Cross-browser testing uitgevoerd
- [ ] Mobile responsiveness getest
- [ ] Performance score >90 behaald
- [ ] GDPR compliance geverifieerd
- [ ] Backup en rollback plan gereed

## 🆘 Troubleshooting

### Veelvoorkomende Problemen

**Calendly laadt niet**:
- Check browser console voor JavaScript errors
- Verificeer Calendly URL
- Test in incognito mode

**Webhook fails**:
- Test webhook URL met Postman
- Controleer API key
- Check n8n workflow logs

**Analytics werkt niet**:
- Verificeer tracking IDs
- Check ad blockers
- Test in Real-Time reports

Voor meer hulp, raadpleeg de README.md of neem contact op met het development team.
