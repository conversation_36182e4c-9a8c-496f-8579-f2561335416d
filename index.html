<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Ontdek NILA's Poetry of Light - Exclusieve handgemaakte albast verlichting. Boek een groepsdemonstratie of vraag samples aan.">
    <meta name="keywords" content="NILA, verlichting, albast, handgemaakt, Poetry of Light, luxe verlichting, architectuur">
    <meta name="author" content="NILA - Poetry of Light">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://landing.wearenila.com/">
    <meta property="og:title" content="NILA - Poetry of Light | Exclusieve Groepsdemonstratie">
    <meta property="og:description" content="Ontdek NILA's unieke handgemaakte albast verlichting. Boek nu uw plek voor onze exclusieve groepsdemonstratie.">
    <meta property="og:image" content="/assets/images/nila-hero-og.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://landing.wearenila.com/">
    <meta property="twitter:title" content="NILA - Poetry of Light | Exclusieve Groepsdemonstratie">
    <meta property="twitter:description" content="Ontdek NILA's unieke handgemaakte albast verlichting. Boek nu uw plek voor onze exclusieve groepsdemonstratie.">
    <meta property="twitter:image" content="/assets/images/nila-hero-og.jpg">

    <!-- Favicon - NILA Logo -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="manifest" href="/site.webmanifest">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600;700&family=DM+Sans:wght@300;400;500;600;700&display=swap&v=2024" as="style">
    <link rel="preload" href="/src/assets/images/nila-flush-hero.jpg" as="image">
    <link rel="preload" href="/src/assets/images/NILA Ivy-min.jpg" as="image">
    <link rel="preload" href="/src/assets/images/nila-craftsmanship.png" as="image">
    <link rel="preload" href="/src/assets/images/nila-the-leaf.png" as="image"> 
    <link rel="preload" href="/src/assets/images/clear-pendant.png" as="image">
    <link rel="preload" href="/src/assets/images/circle-pendant.png" as="image">
    <link rel="preload" href="/src/assets/images/nila-flush.png" as="image">

    <!-- Load Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@400;500;600;700&family=DM+Sans:wght@300;400;500;600;700&display=swap&v=2024" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/src/styles/main.css?v=20241215-1545-button-contrast">
    <link rel="stylesheet" href="/src/assets/images/placeholder.css?v=2024-badge-final">

    <!-- Calendly CSS -->
    <link href="https://assets.calendly.com/assets/external/widget.css" rel="stylesheet">

    <title>NILA - Poetry of Light | Exclusieve Groepsdemonstratie & Samples</title>

    <!-- Canonical URL -->
    <link rel="canonical" href="https://landing.wearenila.com/">

    <!-- Structured Data - Organization -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "NILA - Poetry of Light",
        "url": "https://wearenila.com",
        "logo": "https://landing.wearenila.com/src/assets/images/nila-logo-black.jpg",
        "description": "Exclusieve handgemaakte albast verlichting. Enige fabrikant in Noord-Europa met volledige in-house controle.",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Zijlweg 7",
            "addressLocality": "Waalwijk",
            "postalCode": "5145 NR",
            "addressCountry": "NL"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+31-6-1179-0622",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://www.linkedin.com/company/wearenila/about/",
            "https://www.instagram.com/nila.poetryoflight/"
        ]
    }
    </script>

    <!-- Structured Data - Product -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "NILA Poetry of Light Verlichting",
        "description": "Handgemaakte albast verlichting met unieke Poetry of Light concept. Exclusieve groepsdemonstraties en samples beschikbaar.",
        "brand": {
            "@type": "Brand",
            "name": "NILA"
        },
        "manufacturer": {
            "@type": "Organization",
            "name": "NILA - Poetry of Light"
        },
        "material": "Albast",
        "category": "Luxe Verlichting",
        "offers": {
            "@type": "Offer",
            "availability": "https://schema.org/InStock",
            "priceCurrency": "EUR",
            "seller": {
                "@type": "Organization",
                "name": "NILA - Poetry of Light"
            }
        }
    }
    </script>

    <!-- Structured Data - Event (Demonstration) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Event",
        "name": "NILA Poetry of Light Groepsdemonstratie",
        "description": "Exclusieve 30-minuten online demonstratie van NILA's handgemaakte albast verlichting voor professionals.",
        "organizer": {
            "@type": "Organization",
            "name": "NILA - Poetry of Light",
            "url": "https://wearenila.com"
        },
        "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
        "eventStatus": "https://schema.org/EventScheduled",
        "location": {
            "@type": "VirtualLocation",
            "url": "https://cal.com/nila-poetry-of-light/nila-demo"
        },
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "EUR",
            "availability": "https://schema.org/InStock"
        }
    }
    </script>
</head>
<body class="antialiased">
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-nila-night-black text-white px-4 py-2 rounded-lg z-50">
        Spring naar hoofdinhoud
    </a>
    <!-- Hero Section -->
    <section id="hero" class="relative min-h-screen flex items-center justify-center overflow-hidden" role="banner" aria-label="NILA Poetry of Light hoofdsectie">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0">
            <!-- Mobile: Extreme crop voor lamp zichtbaarheid -->
            <div class="hero-image-container w-full h-full">
                <img
                    src="src/assets/images/nila-flush-hero.jpg"
                    alt="NILA Flush Verlichting - Handgemaakte albast lamp met warme gloed"
                    class="hero-image w-full h-full object-cover"
                    loading="eager"
                    decoding="async"
                    fetchpriority="high"
                >
            </div>
            <div class="hero-overlay absolute inset-0" role="presentation" aria-hidden="true"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-30 section-container text-center text-white">
            <div class="max-w-4xl mx-auto animate-fade-in">
                <!-- Logo/Brand -->
                <div class="mb-8">
                    <img
                        src="/src/assets/images/nila-logo-black.jpg"
                        alt="NILA Logo"
                        class="h-16 mx-auto mb-4 bg-white rounded-lg p-2"
                        loading="eager"
                    >
                    <div class="text-xl font-light tracking-wider subheading" style="color: #CFB5A7;">
                                        </div>
                </div>

                <!-- Main Headline -->
                <h1 class="text-hero font-bold mb-6 text-shadow-lg animate-slide-up">
                    <br>
                    <span style="color: #CFB5A7;">Poetry of Light</span>
                </h1>

                <!-- Subtitle -->
                <p class="text-xl md:text-2xl font-light mb-8 max-w-3xl mx-auto text-shadow animate-slide-up leading-relaxed" style="animation-delay: 0.2s;">
                    Ontdek de mogelijkheden met Nila verlichting tijdens een exclusieve online demonstratie van 30 minuten
                </p>

                <!-- Elegant Call to Action -->
                <div class="text-center mb-8 animate-slide-up" style="animation-delay: 0.3s;">
                    <p class="text-lg text-gray-200 mb-4">
                        Hoe onze verlichting niet alleen een ruimte verlicht-<br>
                        maar onderdeel wordt van haar identiteit
                    </p>
                    <div class="text-sm" style="color: #CFB5A7;">
                        ✨ Exclusieve demonstratie voor professionals
                    </div>
                </div>

                <!-- CTA Buttons with Improved Hierarchy -->
                <div class="hero-cta-buttons flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up" style="animation-delay: 0.4s;">
                    <!-- Primary CTA: Groepsmeeting -->
                    <div class="relative">
                        <button
                            id="cta-group-demo"
                            class="btn-primary-hero"
                            onclick="openGroupDemo()"
                            aria-label="Boek exclusieve groepsdemonstratie van NILA Poetry of Light"
                            role="button"
                        >
                            <span class="flex items-center relative z-10">
                                <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="text-left">
                                    <div class="text-lg font-bold">Ervaar Poetry of Light</div>
                                    <div class="text-sm opacity-90 font-normal">Exclusieve groepsdemonstratie</div>
                                </div>
                            </span>
                        </button>
                        <!-- Badge outside button -->
                        <div class="absolute -top-3 -right-3 bg-red-500 text-white text-xs px-3 py-1.5 rounded-full animate-pulse font-bold shadow-lg z-40" data-demo-urgency>
                            Beperkt!
                        </div>
                    </div>

                    <!-- Secondary CTA: Samples -->
                    <button
                        id="cta-samples"
                        class="btn-secondary-hero"
                        onclick="openSampleForm()"
                        aria-label="Vraag gratis albast samples aan"
                        role="button"
                    >
                        <span class="flex items-center relative z-10">
                            <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            <div class="text-left">
                                <div class="text-lg font-bold">Voel de albast kwaliteit</div>
                                <div class="text-sm opacity-90 font-normal">Gratis samples</div>
                            </div>
                        </span>
                    </button>
                </div>

                <!-- Trust Indicators Below CTAs -->
                <div class="mt-8 flex flex-col sm:flex-row items-center justify-center gap-6 text-sm animate-slide-up" style="animation-delay: 0.5s; color: #CFB5A7;">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Geen verplichtingen</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>200+ tevreden professionals</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Gratis verzending</span>
                    </div>
                </div>

                <!-- Enhanced Social Proof with Urgency -->
                <div class="mt-12 text-center animate-slide-up" style="animation-delay: 0.6s;">
                    <!-- Subtle Urgency Banner -->
                    <div class="bg-gradient-to-r from-gray-800/10 to-gray-700/10 backdrop-blur-sm border border-gray-400/20 rounded-lg p-3 mb-8 max-w-sm mx-auto">
                        <div class="flex items-center justify-center text-nila-alabaster-white">
                            <svg class="w-4 h-4 mr-2 text-nila-natural-beige" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-xs">
                                <div class="font-medium opacity-90">Volgende demonstratie: <span class="text-nila-alabaster-white font-semibold" data-demo-time data-demo-format="short">Morgen 14:00</span></div>
                                <div class="text-xs opacity-80 font-medium" data-demo-availability data-hero-urgency>Nog slechts 3 plaatsen beschikbaar</div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Proof -->
                    <div class="text-sm mb-2 subheading" style="color: #CFB5A7;">
                        Enige fabrikant in Noord-Europa met volledige in-house controle
                    </div>
                    <div class="flex justify-center space-x-1 mb-2" style="color: #DCC8B6;" role="img" aria-label="5 sterren beoordeling">
                        ⭐⭐⭐⭐⭐
                    </div>
                    <div class="text-xs text-nila-natural-beige mb-4 font-medium">
                        "Fixtures die niet alleen verlichten—ze worden onderdeel van de ruimte-identiteit"
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
            <div class="animate-bounce">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Emotional Story Section -->
        <section id="emotional-story" class="section-padding bg-white">
        <div class="section-container">
            <div class="max-w-6xl mx-auto">
                <!-- Story Introduction -->
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-nila-night-black mb-6 animate-on-scroll">
                        Ritme en Flow: De Essentie van Onze Poetry of Light
                    </h2>
                    <p class="text-xl text-nila-night-black max-w-3xl mx-auto animate-on-scroll leading-relaxed">
                        <strong>Ritme</strong> ontstaat door begrip van hoe licht verandert gedurende de dag.
                        <strong>Flow</strong> wordt bereikt door precieze controle over ons productieproces.
                        Samen creëren zij technisch superieure producten die hun omgeving verbeteren door vorm én functie.
                    </p>
                </div>

                <!-- Visual Story Grid -->
                <div class="grid lg:grid-cols-2 gap-16 items-center mb-16">
                    <!-- Image with Lazy Loading -->
                    <div class="animate-on-scroll">
                        <div class="relative">
                            <img
                                src="/src/assets/images/nila-craftsmanship.png"
                                alt="Handgesneden albast verlichting - elk stuk uniek gemaakt door NILA vakmanschap"
                                class="w-full h-96 object-cover rounded-xl shadow-2xl"
                                loading="lazy"
                                decoding="async"
                                width="600"
                                height="400"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl" aria-hidden="true"></div>
                            <div class="absolute bottom-6 left-6 text-white">
                                <p class="text-sm opacity-90">Handgesneden albast</p>
                                <p class="text-lg font-semibold">Elk stuk uniek</p>
                            </div>
                        </div>
                    </div>

                    <!-- Story Content -->
                    <div class="animate-on-scroll">
                        <h3 class="text-2xl font-bold text-nila-night-black mb-6">
                            "De Zachte Gloed Door Albast Verandert Gedurende de Dag"
                        </h3>
                        <blockquote class="text-lg italic text-nila-night-black mb-6 border-l-4 border-nila-natural-beige pl-6">
                            "Deze prachtige albaststeen heeft een unieke textuur en de kracht om licht op een betoverende manier te verspreiden. 
                            Hier kunnen bijvoorbeeld de mooiste armaturen mee gemaakt worden, die zelfs uitgeschakeld net zo opvallend zijn in daglicht als wanneer ze verlicht zijn."
                        </blockquote>
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-nila-natural-beige rounded-full flex items-center justify-center mr-4">
                                <span class="text-nila-night-black font-bold">MH</span>
                            </div>
                            <div>
                                <p class="font-semibold text-nila-night-black">Marco Hendriks</p>
                                <p class="text-sm text-nila-night-black">Sun Styling</p>
                            </div>
                        </div>
                        <div class="bg-nila-alabaster-white p-4 rounded-lg border border-nila-natural-beige">
                            <p class="text-sm text-nila-night-black mb-2">
                                <strong>Resultaat:</strong> Verlichting die onderdeel wordt van de ruimte-identiteit
                            </p>
                            <button onclick="openGroupDemo()" class="text-nila-rose font-semibold hover:text-nila-night-black transition-colors subheading">
                                → Ontdek onze Poetry of Light benadering
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Craftsmanship Section -->
                <div class="grid lg:grid-cols-2 gap-16 items-center">
                    <!-- Content First -->
                    <div class="animate-on-scroll lg:order-2">
                        <h3 class="text-2xl font-bold text-nila-night-black mb-6">
                            Volledige Controle Over Elke Stap<br>
                            <span class="text-nila-rose">Van Zeldzaam Albast Tot Verlichtingsobject</span>
                        </h3>
                        <p class="text-lg text-nila-night-black mb-6 leading-relaxed">
                            Bij NILA ontwerpen en produceren wij high-end verlichting met volledige controle
                            over elke stap van het proces. Wij vormen zeldzaam albast tot verlichtingsobjecten
                            die precisie-engineering combineren met natuurlijke schoonheid.
                        </p>
                        <div class="space-y-4 mb-8">
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-nila-natural-beige rounded-full mr-4"></div>
                                <span class="text-nila-night-black">Elk stuk handgesneden uit zeldzaam albast</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-nila-natural-beige rounded-full mr-4"></div>
                                <span class="text-nila-night-black">In-house engineering en productiecapaciteiten</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-2 h-2 bg-nila-natural-beige rounded-full mr-4"></div>
                                <span class="text-nila-night-black">Optimale lichtdistributie en naadloze integratie</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button onclick="openSampleForm()" class="bg-nila-natural-beige text-nila-night-black px-6 py-3 rounded-lg hover:bg-nila-rose transition-colors subheading">
                                Voel de albast kwaliteit
                            </button>
                            <button onclick="openGroupDemo()" class="border-2 border-nila-night-black text-nila-night-black px-6 py-3 rounded-lg hover:bg-nila-night-black hover:text-nila-alabaster-white transition-colors font-semibold">
                                Ontdek ons productieproces
                            </button>
                        </div>
                    </div>

                    <!-- Image -->
                    <div class="animate-on-scroll lg:order-1">
                        <div class="relative overflow-hidden rounded-xl">
                            <img
                                src="/src/assets/images/nederlands_vakmanschap.png"
                                alt="Nederlands vakmanschap - Philips precisie"
                                class="w-full h-96 object-cover shadow-2xl nederlands-vakmanschap-image animate-on-scroll"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-6 left-6 text-white">
                                <p class="text-sm opacity-90">Nederlands vakmanschap</p>
                                <p class="text-lg font-semibold">Philips precisie</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Visual Gallery Section -->
    <section id="visual-gallery" class="section-padding bg-nila-light-gray">
        <div class="section-container">
            <div class="max-w-6xl mx-auto">
                <!-- Gallery Header -->
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-nila-night-black mb-6 animate-on-scroll">
                        Beleef toepassingen met albast
                    </h2>
                    <p class="text-xl text-nila-night-black max-w-3xl mx-auto animate-on-scroll leading-relaxed">
                        Onze fixtures verlichten niet alleen een ruimte—ze worden onderdeel van haar identiteit.
                        Ontdek hoe precisie-engineering en natuurlijke schoonheid samenkomen in elk handgemaakt stuk albast.
                    </p>
                </div>

                <!-- Main Gallery Grid -->
                <div class="gallery-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <!-- Large Featured Image -->
                    <div class="md:col-span-2 lg:row-span-2 animate-on-scroll">
                        <div class="relative h-96 lg:h-full">
                            <img
                                src="/src/assets/images/circle-pendant.png"
                                alt="NILA The Circle Pendant hangverlichting"
                                class="w-full h-full object-cover rounded-xl shadow-lg"
                                style="object-fit: cover; transform: scale(1); object-position: center;"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-8 left-8">
                                <h3 class="text-2xl font-bold mb-2 text-nila-rose">NILA The Circle Pendant</h3>
                                <p class="text-lg opacity-90 text-white">Hangverlichting</p>
                                <p class="text-sm opacity-75 mt-2 text-white">Handgesneden uit Spaans albast</p>
                            </div>
                        </div>
                    </div>

                    <!-- Smaller Images -->
                    <div class="animate-on-scroll">
                        <div class="relative h-48">
                            <img
                                src="/src/assets/images/nila-flush.png"
                                alt="NILA Flush wandverlichting"
                                class="w-full h-full object-cover rounded-xl shadow-lg"
                                style="object-fit: cover; transform: scale(1); object-position: center;"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-4 left-4">
                                <h4 class="font-semibold text-nila-rose">NILA Flush</h4>
                                <p class="text-sm opacity-90 text-white">Wandverlichting</p>
                            </div>
                        </div>
                    </div>

                    <div class="animate-on-scroll">
                        <div class="relative h-48">
                            <img
                                src="/src/assets/images/NILA Ivy-min.jpg"
                                alt="NILA Ivy Wandverlichting - Handgemaakte albast wandlamp"
                                class="w-full h-full object-cover rounded-xl shadow-lg"
                                style="object-fit: cover; transform: scale(1); object-position: center;"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-4 left-4">
                                <h4 class="font-semibold text-nila-rose">NILA Ivy</h4>
                                <p class="text-sm text-white opacity-90">Wandverlichting</p>
                            </div>
                        </div>
                    </div>

                    <div class="animate-on-scroll">
                        <div class="relative h-48">
                            <img
                                src="/src/assets/images/nila-the-leaf.png"
                                alt="NILA Leaf - Handgemaakte albast wandverlichting"
                                class="w-full h-full object-cover rounded-xl shadow-lg"
                                style="object-fit: cover; transform: scale(1); object-position: center;"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-4 left-4">
                                <h4 class="font-semibold text-nila-rose">NILA Leaf</h4>
                                <p class="text-sm opacity-90 text-white">Wandverlichting</p>
                            </div>
                        </div>
                    </div>

                    <div class="animate-on-scroll">
                        <div class="relative h-48">
                            <img
                                src="/src/assets/images/clear-pendant.png"
                                alt="NILA Clear Pendant - Handgemaakte albast hanglamp"
                                class="w-full h-full object-cover rounded-xl shadow-lg"
                                style="object-fit: cover; transform: scale(1); object-position: center;"
                                loading="lazy"
                                decoding="async"
                            >
                            <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-xl"></div>
                            <div class="absolute bottom-4 left-4">
                                <h4 class="font-semibold text-nila-rose">NILA Clear Pendant</h4>
                                <p class="text-sm opacity-90 text-white">Hangverlichting</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Experience Call to Action -->
                <div class="bg-white p-8 md:p-12 rounded-xl shadow-xl animate-on-scroll">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <div>
                            <h3 class="text-2xl font-bold text-nila-navy mb-4">
                                Ontmoet de founders
                            </h3>
                            <p class="text-lg text-nila-dark-gray mb-6 leading-relaxed">
                                Foto's kunnen de warmte en textuur van albast niet volledig overbrengen.
                                Kom naar onze demonstratie en zie live waarom professionals kiezen voor NILA.
                            </p>
                            <div class="flex items-center text-sm text-nila-dark-gray mb-6">
                                <svg class="w-5 h-5 text-nila-gold mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Exclusief voor professionals - Maximaal 20 deelnemers
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="bg-nila-navy text-white p-6 rounded-lg mb-4">
                                <p class="text-sm text-nila-accent-gold mb-2">Volgende demonstratie</p>
                                <p class="text-2xl font-bold" data-demo-time data-demo-format="week">Deze Week</p>
                                <p class="text-sm opacity-90" data-demo-availability>Neem deel en ontvang een kortingscode</p>
                            </div>
                            <div class="space-y-3">
                                <button onclick="openGroupDemo()" class="bg-nila-accent-gold text-nila-navy px-8 py-3 rounded-lg w-full hover:bg-nila-rose hover:text-white transition-colors font-semibold shadow-lg">
                                    Reserveer Uw Plek
                                </button>
                                <button onclick="openSampleForm()" class="border-2 border-nila-night-black text-nila-night-black px-8 py-3 rounded-lg w-full hover:bg-nila-night-black hover:text-nila-alabaster-white transition-colors font-semibold">
                                    Of vraag eerst samples aan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Elegant Value Proposition Section -->
    <section id="value-proposition" class="section-padding bg-gradient-to-br from-nila-alabaster-white to-nila-natural-beige/30">
        <div class="section-container">
            <div class="max-w-4xl mx-auto">
                <!-- Elegant Header -->
                <div class="text-center mb-16 animate-on-scroll">
                    <h2 class="text-3xl md:text-4xl font-bold text-nila-navy mb-6">
                        Waarom Professionals Kiezen Voor NILA
                    </h2>
                    <p class="text-xl text-nila-dark-gray leading-relaxed">
                        Ontdek wat NILA onderscheidt in de wereld van exclusieve verlichting
                    </p>
                </div>

                <!-- Elegant Features Grid -->
                <div class="grid md:grid-cols-2 gap-12 mb-16">
                    <div class="animate-on-scroll">
                        <div class="bg-white/80 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-nila-natural-beige/20">
                            <div class="w-16 h-16 bg-nila-rose rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-nila-navy mb-4 text-center">Unieke Exclusiviteit</h3>
                            <p class="text-nila-dark-gray leading-relaxed mb-6 text-center">
                                Elk NILA stuk is handgemaakt van zeldzaam albast. Geen massaproductie,
                                maar beperkte collecties die uw projecten onderscheiden.
                            </p>
                            <div class="space-y-3 text-nila-dark-gray">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>Handgemaakt albast - elk stuk uniek</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>Beperkte productie voor exclusiviteit</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>Verhaal dat uw klanten raakt</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="animate-on-scroll">
                        <div class="bg-white/80 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-nila-natural-beige/20">
                            <div class="w-16 h-16 bg-nila-rose rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-nila-navy mb-4 text-center">Betrouwbare Partner</h3>
                            <p class="text-nila-dark-gray leading-relaxed mb-6 text-center">
                                Met 11 jaar ervaring bij Philips/Signify begrijpen wij de professionele
                                verlichtingswereld als geen ander.
                            </p>
                            <div class="space-y-3 text-nila-dark-gray">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>11 jaar Philips/Signify expertise</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>Gegarandeerde levertijden</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-nila-rose rounded-full mr-3"></div>
                                    <span>Persoonlijke begeleiding</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Elegant Stats -->
                <div class="grid md:grid-cols-3 gap-8 text-center" id="stats-section">
                    <div class="bg-white/60 backdrop-blur-sm p-8 rounded-xl shadow-lg animate-on-scroll border border-nila-natural-beige/20">
                        <div class="text-4xl font-bold text-nila-rose mb-2" data-count-to="200" data-count-suffix="+">0+</div>
                        <div class="text-nila-navy font-semibold">Tevreden Professionals</div>
                        <div class="text-sm text-nila-dark-gray mt-2">Vertrouwen NILA</div>
                    </div>
                    <div class="bg-white/60 backdrop-blur-sm p-8 rounded-xl shadow-lg animate-on-scroll border border-nila-natural-beige/20">
                        <div class="text-4xl font-bold text-nila-rose mb-2" data-count-to="11" data-count-suffix="">0</div>
                        <div class="text-nila-navy font-semibold">Jaar Expertise</div>
                        <div class="text-sm text-nila-dark-gray mt-2">Philips/Signify ervaring</div>
                    </div>
                    <div class="bg-white/60 backdrop-blur-sm p-8 rounded-xl shadow-lg animate-on-scroll border border-nila-natural-beige/20">
                        <div class="text-4xl font-bold text-nila-rose mb-2" data-count-to="100" data-count-suffix="%">0%</div>
                        <div class="text-nila-navy font-semibold">Handgemaakt</div>
                        <div class="text-sm text-nila-dark-gray mt-2">In Nederland</div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Elegant Closing Section -->
    <section id="elegant-closing" class="section-padding bg-gradient-to-br from-nila-navy to-black text-white">
        <div class="section-container text-center">
            <div class="max-w-4xl mx-auto">
                <!-- Elegant Header -->
                <div class="mb-12 animate-on-scroll">
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">
                        <span class="text-nila-alabaster-white">Uw Reis Naar Exclusieve Verlichting</span><br>
                        <span class="text-nila-accent-gold">Begint Hier</span>
                    </h2>
                    <p class="text-xl text-gray-300 leading-relaxed">
                        Elke grote transformatie begint met een eerste stap.
                        Voor NILA is dat het moment waarop u onze verlichting voor het eerst ervaart.
                    </p>
                </div>

                <!-- Two Paths -->
                <div class="grid md:grid-cols-2 gap-8 mb-12">
                    <!-- Path 1: Demonstration -->
                    <div class="bg-white/10 backdrop-blur-sm p-8 rounded-xl animate-on-scroll">
                        <div class="w-16 h-16 bg-nila-accent-gold rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">De Volledige Ervaring</h3>
                        <p class="text-gray-300 mb-6">
                            Kom naar onze demonstratie en ervaar hoe NILA verlichting ruimtes transformeert.
                            Voel de textuur, zie het licht, begrijp de kwaliteit.
                        </p>
                        <div class="text-sm text-nila-accent-gold mb-4">
                            ✨ Exclusief voor professionals<br>
                            ⏰ <span data-demo-availability>Neem deel en ontvang een kortingscode</span>
                        </div>
                        <button onclick="openGroupDemo()" class="bg-nila-accent-gold text-nila-navy font-semibold px-8 py-3 rounded-lg w-full hover:bg-yellow-400 transition-colors">
                            Reserveer Uw Demonstratie
                        </button>
                    </div>

                    <!-- Path 2: Samples -->
                    <div class="bg-white/10 backdrop-blur-sm p-8 rounded-xl animate-on-scroll">
                        <div class="w-16 h-16 bg-nila-accent-gold rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Begin Met Samples</h3>
                        <p class="text-gray-300 mb-6">
                            Laat uw klanten de kwaliteit van albast voelen. Onze samples spreken voor zich
                            en overtuigen direct van de unieke uitstraling.
                        </p>
                        <div class="text-sm text-nila-accent-gold mb-4">
                            📦 Gratis verzending<br>
                            🎯 Directe impact op klanten
                        </div>
                        <button onclick="openSampleForm()" class="border-2 border-nila-alabaster-white text-nila-alabaster-white font-semibold px-8 py-3 rounded-lg w-full hover:bg-nila-alabaster-white hover:text-nila-night-black transition-colors">
                            Vraag Samples Aan
                        </button>
                    </div>
                </div>

                <!-- Elegant Promise -->
                <div class="bg-nila-accent-gold/20 backdrop-blur-sm p-8 rounded-xl mb-8 animate-on-scroll">
                    <h3 class="text-2xl font-bold text-nila-accent-gold mb-4">
                        Onze Belofte Aan U
                    </h3>
                    <p class="text-lg text-gray-200 leading-relaxed">
                        Wij geloven zo sterk in de kracht van onze verlichting, dat we u uitnodigen om het
                        <em>zonder enige verplichting</em> te ervaren. Uw tevredenheid is onze prioriteit.
                    </p>
                </div>

                <!-- Subtle Urgency -->
                <div class="text-center animate-on-scroll">
                    <div class="text-nila-accent-gold text-sm mb-2">
                        <span data-demo-time data-demo-format="week">Volgende demonstratie deze week</span> - <span data-demo-availability>Neem deel en ontvang een kortingscode</span>
                    </div>
                    <div class="text-nila-natural-beige text-xs font-medium">
                        Sluit u aan bij 200+ professionals die al de NILA ervaring hebben
                    </div>
                </div>
            </div>
        </div>
    </section>
    </main>

    <!-- Footer -->
    <footer class="bg-nila-night-black text-nila-alabaster-white py-12">
        <div class="section-container">
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Company Info -->
                <div>
                    <img
                        src="/src/assets/images/nila-logo-black.jpg"
                        alt="NILA Logo"
                        class="h-12 mb-4 bg-nila-alabaster-white rounded-lg p-2"
                    >
                    <p class="text-nila-natural-beige mb-4">
                        NILA - Poetry of Light<br>
                        Zeldzaam albast verlichting<br>
                        Volledige in-house controle
                    </p>
                    <div class="flex space-x-4">
                        <!-- LinkedIn -->
                        <a href="https://www.linkedin.com/company/wearenila/about/" target="_blank" rel="noopener noreferrer" class="text-nila-rose hover:text-nila-alabaster-white transition-colors" aria-label="Volg NILA op LinkedIn">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <!-- Instagram -->
                        <a href="https://www.instagram.com/nila.poetryoflight/" target="_blank" rel="noopener noreferrer" class="text-nila-rose hover:text-nila-alabaster-white transition-colors" aria-label="Volg NILA op Instagram">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-nila-rose subheading">Contact</h4>
                    <div class="space-y-2 text-nila-natural-beige">
                        <p>📍 Zijlweg 7, 5145 NR Waalwijk</p>
                        <p>📧 <EMAIL></p>
                        <p>📞 +31 (0)6 1179 0622</p>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-nila-rose subheading">Meer Informatie</h4>
                    <div class="space-y-2">
                        <a href="https://wearenila.com/collectie/" target="_blank" rel="noopener noreferrer" class="block text-nila-natural-beige hover:text-nila-alabaster-white transition-colors">Collectie</a>
                        <a href="https://wearenila.com/ons-ambacht/" target="_blank" rel="noopener noreferrer" class="block text-nila-natural-beige hover:text-nila-alabaster-white transition-colors">Ons Ambacht</a>
                        <a href="https://wearenila.com/over-ons/" target="_blank" rel="noopener noreferrer" class="block text-nila-natural-beige hover:text-nila-alabaster-white transition-colors">Over Ons</a>
                        <a href="https://wearenila.com/contact/" target="_blank" rel="noopener noreferrer" class="block text-nila-natural-beige hover:text-nila-alabaster-white transition-colors">Contact</a>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-nila-natural-beige mt-8 pt-8 text-center text-nila-natural-beige">
                <p>&copy; 2025 NILA - Poetry of Light. Alle rechten voorbehouden.</p>
                <div class="mt-2 space-x-4">
                    <a href="#" onclick="openPrivacyModal(); return false;" class="hover:text-nila-alabaster-white transition-colors cursor-pointer">Privacy Policy</a>
                    <a href="#" onclick="openTermsModal(); return false;" class="hover:text-nila-alabaster-white transition-colors cursor-pointer">Algemene Voorwaarden</a>
                    <a href="#" onclick="openPrivacyModal(); return false;" class="hover:text-nila-alabaster-white transition-colors cursor-pointer">Cookie Beleid</a>
                </div>

                <!-- Built with love -->
                <div class="mt-4 text-sm">
                    <p>Built with love by <a href="mailto:<EMAIL>" class="text-nila-rose hover:text-nila-alabaster-white transition-colors">RT Projects</a></p>
                </div>

                <!-- Hidden link -->
                <a href="https://property-plaza.com" class="opacity-0 absolute -left-full" aria-hidden="true" tabindex="-1">Property Plaza</a>
            </div>
        </div>
    </footer>

    <!-- Modern Drawer-Style CTA Bar -->
    <div id="sticky-cta-bar" class="fixed bottom-0 left-0 right-0 z-50 hidden transform translate-y-full transition-transform duration-300">
        <div class="mx-4 mb-4 bg-gradient-to-r from-nila-night-black via-gray-900 to-nila-night-black backdrop-blur-lg rounded-2xl shadow-2xl border border-nila-natural-beige/20">
            <!-- Drawer Handle -->
            <div class="flex justify-center pt-2">
                <div class="w-12 h-1 bg-gray-400 rounded-full opacity-50"></div>
            </div>

            <div class="px-4 pb-4 pt-2">
                <!-- Begeleidende tekst bovenaan -->
                <div class="text-center text-white mb-3">
                    <div class="flex items-center justify-center mb-1">
                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                        <div class="font-semibold text-sm" data-demo-urgency>Beperkte plaatsen beschikbaar</div>
                    </div>
                    <div class="text-xs opacity-75">Volgende demonstratie: <span data-demo-time data-demo-format="short">Morgen 14:00</span></div>
                </div>

                <!-- Buttons naast elkaar -->
                <div class="flex gap-3 justify-center">
                    <button
                        onclick="openGroupDemo()"
                        class="bg-nila-natural-beige hover:bg-nila-rose text-nila-night-black font-bold py-3 px-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 flex-1 sm:flex-initial sm:px-6"
                        aria-label="Boek demonstratie nu"
                    >
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-semibold">Boek Demo</span>
                        </div>
                    </button>
                    <button
                        onclick="openSampleForm()"
                        class="bg-transparent border-2 border-nila-alabaster-white text-nila-alabaster-white hover:bg-nila-alabaster-white hover:text-nila-night-black font-bold py-3 px-4 rounded-xl transition-all duration-300 flex-1 sm:flex-initial sm:px-6"
                        aria-label="Vraag samples aan"
                    >
                        <div class="flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            <span class="text-sm font-semibold">Vraag Samples</span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating CTA Button (Fallback for smaller screens) -->
    <div id="floating-cta" class="fixed bottom-6 right-6 z-40 hidden lg:hidden">
        <button
            onclick="openGroupDemo()"
            class="bg-nila-natural-beige hover:bg-nila-rose text-nila-night-black font-semibold py-3 px-5 rounded-full shadow-xl transform hover:scale-105 transition-all duration-300"
            aria-label="Boek demonstratie - floating button"
        >
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <div class="text-sm font-semibold subheading">Demo</div>
                    <div class="text-xs opacity-90">Nu boeken</div>
                </div>
            </div>
        </button>
    </div>

    <!-- Sample Form Modal -->
    <div id="sample-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <!-- Close Button -->
            <button onclick="closeSampleForm()" class="close-button" aria-label="Sluit formulier"></button>

            <!-- Modal Header -->
            <div class="modal-header">
                <h2>Vraag uw persoonlijke samples aan</h2>
                <p>Ervaar de kwaliteit van onze albast verlichting met gratis samples direct bij u thuis.</p>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <!-- Sample Form -->
                <form id="sample-form">
                    <!-- Name Fields -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="firstName">Voornaam *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">Achternaam *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>

                    <!-- Company -->
                    <div class="form-group">
                        <label for="company">Bedrijfsnaam</label>
                        <input type="text" id="company" name="company">
                    </div>

                    <!-- Contact Info -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="email">E-mailadres *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Telefoonnummer</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="form-group">
                        <label for="street">Straat en huisnummer *</label>
                        <input type="text" id="street" name="street" required>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="postalCode">Postcode *</label>
                            <input type="text" id="postalCode" name="postalCode" required>
                        </div>
                        <div class="form-group">
                            <label for="city">Plaats *</label>
                            <input type="text" id="city" name="city" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="country">Land *</label>
                        <select id="country" name="country" required>
                            <option value="Nederland">Nederland</option>
                            <option value="België">België</option>
                            <option value="Duitsland">Duitsland</option>
                            <option value="Frankrijk">Frankrijk</option>
                            <option value="Overig">Overig</option>
                        </select>
                    </div>

                    <!-- Project Details -->
                    <div class="form-group">
                        <label for="projectDetails">Projectdetails (optioneel)</label>
                        <textarea id="projectDetails" name="projectDetails" rows="3"
                                  placeholder="Vertel ons over uw project..."></textarea>
                    </div>

                    <!-- GDPR Consent -->
                    <div class="checkbox-group">
                        <input type="checkbox" id="gdprConsent" name="gdprConsent" required>
                        <label for="gdprConsent">
                            Ik ga akkoord met het <a href="#" onclick="openPrivacyModal(); return false;" class="text-nila-rose hover:underline cursor-pointer">privacybeleid</a>
                            en geef toestemming voor het verwerken van mijn gegevens voor het toesturen van samples. *
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" id="submit-sample-form">
                        <span id="submit-text">Samples aanvragen</span>
                        <span id="submit-loading" class="hidden">
                            <div class="loading-spinner inline-block mr-2"></div>
                            Bezig met verzenden...
                        </span>
                    </button>
                </form>
            </div>
            <!-- End Modal Body -->
        </div>
        </div>
    </div>

    <!-- Cal.com Embed Script -->
    <script type="text/javascript">
        (function (C, A, L) {
            let p = function (a, ar) {
                a.q.push(ar);
            };
            let d = C.document;
            C.Cal = C.Cal || function () {
                let cal = C.Cal;
                let ar = arguments;
                if (!cal.loaded) {
                    cal.ns = {};
                    cal.q = cal.q || [];
                    d.head.appendChild(d.createElement("script")).src = A;
                    cal.loaded = true;
                }
                if (ar[0] === L) {
                    const api = function () {
                        p(api, arguments);
                    };
                    const namespace = ar[1];
                    api.q = api.q || [];
                    typeof namespace === "string" ? (cal.ns[namespace] = api) && p(api, ar) : p(cal, ar);
                    return;
                }
                p(cal, ar);
            };
        })(window, "https://app.cal.com/embed/embed.js", "init");

        Cal("init", {origin:"https://app.cal.com"});

        Cal("ui", {
            "styles": {
                "branding": {
                    "brandColor": "#CFB5A7",
                    "lightColor": "#F8F4EC",
                    "lighterColor": "#DCC8B6",
                    "lightestColor": "#F8F4EC",
                    "highlightColor": "#CFB5A7",
                    "mediumColor": "#DCC8B6"
                }
            },
            "hideEventTypeDetails": false,
            "layout": "month_view",
            "theme": "light"
        });
    </script>

    <!-- Cal.com integration ready - buttons use onclick handlers -->

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="sticky top-0 bg-white border-b border-gray-200 p-6 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-nila-night-black">Privacy Policy</h2>
                <button id="closePrivacyModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>
            <div class="p-6 space-y-6 text-nila-night-black">
                <div>
                    <h3 class="text-lg font-semibold mb-3">INLEIDING</h3>
                    <p>NILA ("wij", "ons", "onze") respecteert uw privacy en is toegewijd aan de bescherming ervan. Dit Privacybeleid legt uit hoe wij uw persoonsgegevens verzamelen, gebruiken, delen en beschermen en hoe u uw privacyrechten kunt uitoefenen. Het is van toepassing op alle persoonsgegevens die wij verzamelen via onze website en andere gerelateerde diensten.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">GEGEVENS DIE WE VERZAMELEN</h3>
                    <p>We kunnen informatie verzamelen die u ons rechtstreeks verschaft, zoals uw naam, e-mailadres, en andere contactinformatie. Tevens kunnen we informatie verzamelen over uw gebruik van onze website, middels cookies en soortgelijke technologieën.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">COOKIES EN VERGELIJKBARE TECHNOLOGIEËN</h3>
                    <p>NILA gebruikt cookies en vergelijkbare technologieën (zoals webbakens en pixels) om informatie over uw gebruik van onze website te verzamelen. Dit omvat informatie over bezochte pagina's, de doorgebrachte tijd, geklikte links en andere relevante data over uw browser, apparaat, en internetverbinding. We verzamelen ook gegevens over uw interactie met onze e-mails, zoals het openen ervan of het klikken op links daarin.</p>
                    <p class="mt-3">We gebruiken cookies van derden, zoals Meta, Google Analytics, Google Web Fonts, en Google Tag Manager. We overwegen ook het gebruik van Hotjar en Leadfeeder in de toekomst. Deze externe partijen kunnen eveneens gegevens over uw websitegebruik verzamelen, die gecombineerd kunnen worden met andere data die zij over u hebben.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">HOE WE UW GEGEVENS GEBRUIKEN</h3>
                    <p>De verzamelde informatie stelt ons in staat om onze website en diensten te optimaliseren, personaliseren, met u te communiceren, analyses uit te voeren en ons te beschermen tegen fraude en andere onwettige praktijken.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">UW RECHTEN</h3>
                    <p>U heeft recht op toegang, rectificatie, verwijdering van uw persoonsgegevens, het indienen van bezwaren tegen verwerking ervan en het aanvechten bij een toezichthoudende instantie.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">HOE WE UW GEGEVENS BESCHERMEN</h3>
                    <p>NILA heeft adequate technische en organisatorische strategieën geïmplementeerd om uw persoonlijke data te beschermen tegen onbevoegde toegang, gebruik, openbaarmaking, wijziging en vernietiging.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">WIJZIGINGEN IN DIT PRIVACYBELEID</h3>
                    <p>Dit Privacybeleid kan periodiek worden geüpdatet om aan te geven hoe we met persoonsgegevens omgaan. Bij significante wijzigingen informeren we u via onze website of andere kanalen.</p>
                </div>

                <div class="text-sm text-gray-600 border-t pt-4">
                    <p><em>Laatste update: 28 augustus 2023</em></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms Modal -->
    <div id="termsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="sticky top-0 bg-white border-b border-gray-200 p-6 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-nila-night-black">Algemene Voorwaarden</h2>
                <button id="closeTermsModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>
            <div class="p-6 space-y-6 text-nila-night-black">
                <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-nila-rose">
                    <p><strong>NILA</strong> is gevestigd aan Bruistensingel 500, 5232 AH 's-Hertogenbosch, ingeschreven in het handelsregister van de Kamer van Koophandel onder het nummer 90434943.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">1. DEFINITIES</h3>
                    <p><strong>Product:</strong> een op maat gemaakte lamp die na bestelling wordt vervaardigd.</p>
                    <p><strong>Klant:</strong> de (rechts-)persoon die een product van NILA koopt.</p>
                    <p><strong>Leverancier:</strong> NILA, die het product verkoopt en levert.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">2. ALGEMEEN</h3>
                    <p><strong>2.1.</strong> Deze voorwaarden zijn van toepassing op alle aanbiedingen, werkzaamheden, offertes en overeenkomsten voor producten gemaakt door NILA tussen de leverancier en de klant.</p>
                    <p><strong>2.2.</strong> Afwijkingen op deze algemene voorwaarden zijn slechts geldig indien uitdrukkelijk schriftelijk overeengekomen door NILA.</p>
                    <p><strong>2.3.</strong> Bij onduidelijkheid over de uitleg van één of meerdere bepalingen van deze algemene voorwaarden, vindt uitleg plaats 'naar de geest' van deze bepalingen.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">3. BESTELLING EN LEVERING</h3>
                    <p><strong>3.1.</strong> Bestellingen worden definitief na bevestiging door de klant en acceptatie door NILA.</p>
                    <p><strong>3.2.</strong> Gezien het maatwerk karakter van de lampen vervalt het herroepingsrecht.</p>
                    <p><strong>3.3.</strong> De klant dient alle benodigde informatie voor het vervaardigen van het product tijdig te verstrekken. NILA is niet verantwoordelijk voor vertragingen of defecten als gevolg van onjuiste of onvolledige informatie verstrekt door de klant.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">4. PRIJZEN EN BETALING</h3>
                    <p><strong>4.1.</strong> De prijzen van de producten zijn gebaseerd op de op dat moment bekende kostenfactoren. Eventuele veranderingen kunnen leiden tot prijswijzigingen.</p>
                    <p><strong>4.2.</strong> Betalingen dienen binnen 14 dagen na de factuurdatum te worden voldaan, tenzij anders overeengekomen.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">5. ANNULERING EN TERUGBETALING</h3>
                    <p><strong>5.1.</strong> Gezien het maatwerk karakter van de producten, kunnen deze niet worden geretourneerd of geannuleerd na bevestiging van de bestelling.</p>
                    <p><strong>5.2.</strong> Bij annulering na bevestiging, maar voordat de vervaardiging begint, wordt 50% van de totale prijs in rekening gebracht.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">6. GARANTIE EN KLACHTEN</h3>
                    <p><strong>6.1.</strong> NILA waarborgt de kwaliteit van haar producten. Eventuele klachten kunnen binnen een redelijke termijn na ontvangst worden gemeld.</p>
                    <p><strong>6.2.</strong> Beschadigingen die zijn ontstaan door oneigenlijk gebruik vallen niet onder de garantie.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">7. INTELLECTUEEL EIGENDOM</h3>
                    <p><strong>7.1.</strong> Alle ontwerpen, afbeeldingen, en teksten van NILA zijn beschermd onder intellectuele eigendomsrechten en mogen niet zonder uitdrukkelijke schriftelijke toestemming worden gebruikt of gereproduceerd.</p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-3">8. GESCHILLEN EN TOEPASSELIJK RECHT</h3>
                    <p><strong>8.1.</strong> Op deze algemene voorwaarden en alle overeenkomsten is Nederlands recht van toepassing.</p>
                    <p><strong>8.2.</strong> Geschillen worden voorgelegd aan de bevoegde rechter in het arrondissement waar NILA is gevestigd, tenzij de wet anders voorschrijft.</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="./src/scripts/main.js"></script>

    <!-- Drawer Swipe Functionality -->
    <script>
        // Add swipe-to-dismiss functionality to sticky drawer
        function initializeDrawerSwipe() {
            const stickyBar = document.getElementById('sticky-cta-bar');
            if (!stickyBar) return;

            let startY = 0;
            let currentY = 0;
            let isDragging = false;
            let initialTransform = 0;

            // Touch start
            stickyBar.addEventListener('touchstart', function(e) {
                startY = e.touches[0].clientY;
                currentY = startY;
                isDragging = true;
                initialTransform = 0;

                // Add transition class for smooth movement
                stickyBar.style.transition = 'none';
            }, { passive: true });

            // Touch move
            stickyBar.addEventListener('touchmove', function(e) {
                if (!isDragging) return;

                currentY = e.touches[0].clientY;
                const deltaY = currentY - startY;

                // Only allow downward swipes (positive deltaY)
                if (deltaY > 0) {
                    const translateY = Math.min(deltaY, 100); // Limit max drag distance
                    stickyBar.style.transform = `translateY(${translateY}px)`;
                }
            }, { passive: true });

            // Touch end
            stickyBar.addEventListener('touchend', function(e) {
                if (!isDragging) return;

                const deltaY = currentY - startY;
                isDragging = false;

                // Restore transition
                stickyBar.style.transition = 'transform 0.3s ease-out';

                // If dragged down more than 50px, dismiss the drawer
                if (deltaY > 50) {
                    // Hide the drawer
                    if (window.hideStickyBar) {
                        window.hideStickyBar();
                    } else {
                        stickyBar.style.transform = 'translateY(100%)';
                        setTimeout(() => {
                            stickyBar.classList.add('hidden');
                        }, 300);
                    }
                } else {
                    // Snap back to original position
                    stickyBar.style.transform = 'translateY(0)';
                }
            }, { passive: true });

            // Mouse events for desktop testing
            stickyBar.addEventListener('mousedown', function(e) {
                startY = e.clientY;
                currentY = startY;
                isDragging = true;
                initialTransform = 0;
                stickyBar.style.transition = 'none';
                e.preventDefault();
            });

            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                currentY = e.clientY;
                const deltaY = currentY - startY;

                if (deltaY > 0) {
                    const translateY = Math.min(deltaY, 100);
                    stickyBar.style.transform = `translateY(${translateY}px)`;
                }
            });

            document.addEventListener('mouseup', function(e) {
                if (!isDragging) return;

                const deltaY = currentY - startY;
                isDragging = false;

                stickyBar.style.transition = 'transform 0.3s ease-out';

                if (deltaY > 50) {
                    if (window.hideStickyBar) {
                        window.hideStickyBar();
                    } else {
                        stickyBar.style.transform = 'translateY(100%)';
                        setTimeout(() => {
                            stickyBar.classList.add('hidden');
                        }, 300);
                    }
                } else {
                    stickyBar.style.transform = 'translateY(0)';
                }
            });
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit for the main script to initialize
            setTimeout(initializeDrawerSwipe, 500);
        });
    </script>
</body>
</html>
