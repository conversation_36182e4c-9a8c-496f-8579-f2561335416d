#!/bin/bash

# NILA Landing Page Deployment Script
# Author: NILA Development Team
# Version: 1.0.0

set -e  # Exit on any error

echo "🚀 NILA Landing Page - Deployment Script"
echo "========================================"

# Configuration
BUILD_DIR="dist"
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    log_success "Node.js is installed: $(node --version)"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm first."
        exit 1
    fi
    log_success "npm is installed: $(npm --version)"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    npm ci --production=false
    log_success "Dependencies installed successfully"
}

# Run tests (if available)
run_tests() {
    log_info "Running tests..."
    if npm run test --if-present; then
        log_success "All tests passed"
    else
        log_warning "No tests found or tests failed"
    fi
}

# Build for production
build_production() {
    log_info "Building for production..."
    
    # Clean previous build
    if [ -d "$BUILD_DIR" ]; then
        log_info "Cleaning previous build..."
        rm -rf "$BUILD_DIR"
    fi
    
    # Build
    npm run build
    
    if [ -d "$BUILD_DIR" ]; then
        log_success "Production build completed successfully"
    else
        log_error "Build failed - $BUILD_DIR directory not found"
        exit 1
    fi
}

# Validate build
validate_build() {
    log_info "Validating build..."
    
    # Check if index.html exists
    if [ ! -f "$BUILD_DIR/index.html" ]; then
        log_error "index.html not found in build directory"
        exit 1
    fi
    
    # Check if assets directory exists
    if [ ! -d "$BUILD_DIR/assets" ]; then
        log_warning "Assets directory not found - this might be expected"
    fi
    
    # Check file sizes
    INDEX_SIZE=$(stat -f%z "$BUILD_DIR/index.html" 2>/dev/null || stat -c%s "$BUILD_DIR/index.html" 2>/dev/null)
    if [ "$INDEX_SIZE" -lt 1000 ]; then
        log_warning "index.html seems very small ($INDEX_SIZE bytes) - please verify"
    fi
    
    log_success "Build validation completed"
}

# Create backup of current deployment (if exists)
create_backup() {
    if [ -d "current_deployment" ]; then
        log_info "Creating backup of current deployment..."
        cp -r current_deployment "$BACKUP_DIR"
        log_success "Backup created: $BACKUP_DIR"
    fi
}

# Deploy to staging/production
deploy() {
    log_info "Deploying to server..."
    
    # Create deployment directory
    mkdir -p current_deployment
    
    # Copy build files
    cp -r "$BUILD_DIR"/* current_deployment/
    
    log_success "Deployment completed successfully"
    log_info "Files deployed to: $(pwd)/current_deployment"
}

# Cleanup
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add any cleanup tasks here
    log_success "Cleanup completed"
}

# Main deployment process
main() {
    echo
    log_info "Starting deployment process..."
    echo
    
    # Pre-deployment checks
    check_node
    check_npm
    
    # Install and build
    install_dependencies
    run_tests
    build_production
    validate_build
    
    # Deploy
    create_backup
    deploy
    cleanup
    
    echo
    log_success "🎉 Deployment completed successfully!"
    echo
    log_info "Next steps:"
    echo "  1. Test the deployment in current_deployment/"
    echo "  2. Configure your web server to serve from current_deployment/"
    echo "  3. Update DNS settings if needed"
    echo "  4. Monitor analytics and performance"
    echo
    log_warning "Don't forget to:"
    echo "  - Update Calendly URLs in production config"
    echo "  - Update webhook endpoints"
    echo "  - Configure analytics tracking IDs"
    echo "  - Set up SSL certificate"
    echo
}

# Handle script arguments
case "${1:-deploy}" in
    "build")
        log_info "Building only..."
        check_node
        check_npm
        install_dependencies
        build_production
        validate_build
        log_success "Build completed"
        ;;
    "test")
        log_info "Running tests only..."
        check_node
        check_npm
        install_dependencies
        run_tests
        ;;
    "deploy"|"")
        main
        ;;
    "help"|"-h"|"--help")
        echo "NILA Landing Page Deployment Script"
        echo
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  deploy    Full deployment process (default)"
        echo "  build     Build for production only"
        echo "  test      Run tests only"
        echo "  help      Show this help message"
        echo
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
