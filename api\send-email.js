// Vercel Serverless Function for sending emails via Resend
export default async function handler(req, res) {
    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    try {
        const { contact, projectDetails, timestamp } = req.body;

        // Validate required fields
        if (!contact || !contact.firstName || !contact.email) {
            return res.status(400).json({ 
                error: 'Missing required contact information' 
            });
        }

        // Get Resend API key from environment
        const resendApiKey = process.env.RESEND_API_KEY;
        if (!resendApiKey) {
            console.error('❌ RESEND_API_KEY not found in environment variables');
            return res.status(500).json({ 
                error: 'Email service configuration error' 
            });
        }

        // Create email content
        const emailHtml = createEmailTemplate({ contact, projectDetails, timestamp });
        const emailText = createEmailText({ contact, projectDetails, timestamp });

        // Send email via Resend
        const response = await fetch('https://api.resend.com/emails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${resendApiKey}`
            },
            body: JSON.stringify({
                from: 'Luna - NILA <<EMAIL>>',
                to: ['<EMAIL>'],
                subject: `🎨 Nieuwe Sample Aanvraag - ${contact.firstName} ${contact.lastName}`,
                html: emailHtml,
                text: emailText
            })
        });

        if (!response.ok) {
            const errorData = await response.text();
            console.error('❌ Resend API Error:', response.status, errorData);
            return res.status(500).json({ 
                error: 'Failed to send email',
                details: errorData 
            });
        }

        const result = await response.json();
        console.log('✅ Email sent successfully:', result.id);

        return res.status(200).json({ 
            success: true, 
            id: result.id,
            message: 'Sample aanvraag succesvol verstuurd naar Luna!'
        });

    } catch (error) {
        console.error('❌ Server error:', error);
        return res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}

// Create HTML email template
function createEmailTemplate({ contact, projectDetails, timestamp }) {
    return `
    <html>
    <head></head>
    <body>
        <div style="padding: 25px 8%; font-family: Arial, sans-serif; font-size: 15px; line-height: 1.6; color: #333333;">

            <p>Beste Luna,</p>

            <p>Hierbij ontvangt u een nieuwe sample aanvraag via de NILA landing page. Onderstaand vindt u alle benodigde gegevens:</p>

            <p><strong>Klantgegevens:</strong><br>
            Naam: ${contact.firstName} ${contact.lastName}<br>
            ${contact.company ? `Bedrijf: ${contact.company}<br>` : ''}
            </p>

            <p><strong>Adresgegevens:</strong><br>
            Verzendadres:<br>
            ${contact.address.street}<br>
            ${contact.address.postalCode} ${contact.address.city}<br>
            ${contact.address.country}</p>

            <p><strong>Contactgegevens:</strong><br>
            E-mailadres: <a href="mailto:${contact.email}" style="color: #1155cc; text-decoration: none;">${contact.email}</a><br>
            ${contact.phone ? `Telefoonnummer: <a href="tel:${contact.phone}" style="color: #1155cc; text-decoration: none;">${contact.phone}</a><br>` : ''}
            </p>

            ${projectDetails ? `
            <p><strong>Projectomschrijving:</strong><br>
            "${projectDetails}"</p>
            ` : ''}

            <p><strong>Aanvraagdetails:</strong><br>
            Datum & tijd: ${new Date(timestamp).toLocaleDateString('nl-NL', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            })}<br>
            Bron: NILA Landing Page (discovernila.com)</p>

        </div>

        <br>

        <div style="font-family: Arial, sans-serif; font-size: 15px; color: #333333; line-height: 1.6; padding: 0 8%;">
            <div>Met vriendelijke groet,</div>
            <div><br></div>
            <div><strong>NILA - Poetry of Light</strong></div>
            <div>Zijlweg 7, 5145 NR Waalwijk | Nederland</div>
            <div>📧 <a href="mailto:<EMAIL>" style="color: #1155cc; text-decoration: none;"><EMAIL></a></div>
            <div>🌐 <a href="https://wearenila.com" style="color: #1155cc; text-decoration: none;">wearenila.com</a></div>
            <div style="margin-top: 15px; font-size: 12px; color: #999999;">Dit bericht is automatisch <NAME_EMAIL></div>
        </div>

    </body>
    </html>
    `;
}

// Create plain text version
function createEmailText({ contact, projectDetails, timestamp }) {
    return `
NIEUWE SAMPLE AANVRAAG - NILA Poetry of Light

Beste Luna,

Hierbij ontvangt u een nieuwe sample aanvraag via de NILA landing page.

KLANTGEGEVENS:
Naam: ${contact.firstName} ${contact.lastName}
${contact.company ? `Bedrijf: ${contact.company}` : ''}

ADRESGEGEVENS:
Verzendadres:
${contact.address.street}
${contact.address.postalCode} ${contact.address.city}
${contact.address.country}

CONTACTGEGEVENS:
E-mailadres: ${contact.email}
${contact.phone ? `Telefoonnummer: ${contact.phone}` : ''}

${projectDetails ? `PROJECTOMSCHRIJVING:\n"${projectDetails}"\n` : ''}

AANVRAAGDETAILS:
Datum & tijd: ${new Date(timestamp).toLocaleDateString('nl-NL', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
})}
Bron: NILA Landing Page (discovernila.com)

---
NILA - Poetry of Light
Zijlweg 7, 5145 NR Waalwijk
<EMAIL>
wearenila.com

Dit bericht is automatisch <NAME_EMAIL>
    `.trim();
}
