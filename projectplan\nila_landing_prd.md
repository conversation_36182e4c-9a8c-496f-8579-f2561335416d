# Product Requirements Document (PRD)
## NILA Sales Landing Page

---

## 1. Project Overview

### 1.1 Product Name
NILA Sales Landing Page - "Poetry of Light Experience"

### 1.2 Product Vision
Een high-converting landingspagina die de elegante esthetiek van wearenila.com behoudt terwijl het prospects effectief naar twee hoofdacties leidt: het boeken van een groepsdemonstration en het aanvragen van product samples.

### 1.3 Success Metrics
- **Primair**: Conversie rate naar CTA's (target: >15%)
- **Secundair**: Time on page (target: >2 minuten)
- **Tertiair**: Lead quality score (gebaseerd op vervolgacties)

---

## 2. Target Audience

### 2.1 Primaire Doelgroep
- **Architecten** (35-55 jaar)
- **Interior designers** (28-50 jaar) 
- **Retailers** in luxe verlichtingssegment
- **Projectontwikkelaars** (40-60 jaar)

### 2.2 User Personas
**Persona 1: "Saskia de Architect"**
- Zoekt innovatieve verlichtingsoplossingen voor premium projecten
- Wil producten fysiek ervaren voordat ze specificeert
- Prefereert persoonlijke consulatie

**Persona 2: "Mark de Retailer"**
- Wil zijn assortiment uitbreiden met exclusieve merken
- Heeft behoefte aan product samples voor showroom
- Zoekt sterke marge-mogelijkheden

---

## 3. Functionele Requirements

### 3.1 Hero Section
**Must-have:**
- H1: "Ontdek NILA's Poetry of Light" (of vergelijkbaar)
- Subtitle met unieke waardepropostie
- Hero afbeelding van wearenila.com (hergebruik bestaande content)
- Beide CTA's prominent zichtbaar "above the fold"

### 3.2 CTA 1: Online Groepsmeeting
**Functionaliteit:**
- "Sluit aan bij onze exclusieve groepsdemonstratie"
- Calendly integratie voor direct boeken
- Beschikbare timeslots tonen
- Automatische bevestigingsmail

**Technische specs:**
- Calendly embed of popup
- Event tracking (Google Analytics/Facebook Pixel)
- GDPR compliance checkbox

### 3.3 CTA 2: Sample Aanvraag
**Functionaliteit:**
- "Vraag uw persoonlijke samples aan"
- Custom popup form met velden:
  - Voor- en achternaam (verplicht)
  - Bedrijfsnaam (optioneel)
  - Volledig adres (verplicht voor verzending)
  - Email (verplicht)
  - Telefoon (optioneel)
  - Projectdetails (textarea, optioneel)

**Technische specs:**
- Responsive popup design
- Form validatie client-side
- Webhook naar n8n bij successful submit
- JSON payload format:
```json
{
  "timestamp": "2025-05-29T10:30:00Z",
  "source": "nila_landing_samples",
  "contact": {
    "firstName": "string",
    "lastName": "string",
    "company": "string",
    "email": "string",
    "phone": "string",
    "address": {
      "street": "string",
      "city": "string",
      "postalCode": "string",
      "country": "string"
    }
  },
  "projectDetails": "string"
}
```

### 3.4 Content Sections
1. **Hero Section** (met beide CTA's)
2. **Waardepropostie** (hergebruik "Poetry of Light" content)
3. **Product Showcase** (afbeeldingen van wearenila.com)
4. **Social Proof** (testimonials/projecten)
5. **Urgentie Section** (beperkte beschikbaarheid)
6. **Footer** (minimaal, contact info)

---

## 4. Design Requirements

### 4.1 Visual Identity
**Kleurenpalet** (gebaseerd op wearenila.com):
- Primair: Warm wit (#FAF8F5)
- Secundair: Diep blauw/navy (#1E2A5E)  
- Accent: Goud/brass (#B8860B)
- Tekst: Donkergrijs (#2C2C2C)

### 4.2 Typography
- Hoofdteksten: Elegant serif (Playfair Display of vergelijkbaar)
- Body tekst: Clean sans-serif (Montserrat of vergelijkbaar)
- Hiërarchie consistent met wearenila.com

### 4.3 Imagery
**Verplicht hergebruik van bestaande beelden:**
- Hero: PBF6367-1024x768.jpg (albast lamp close-up)
- Product showcase: PBF6715-HDR-1024x768.jpg (sfeershot)
- Supporting: PBF6097-768x1024.jpg (detail shot)

### 4.4 Layout Principles
- **Mobile-first responsive design**
- **Loading tijd <3 seconden**
- **Scannable content** (korte paragraphs, witruimte)
- **Visual hierarchy** die naar CTA's leidt

---

## 5. Technical Requirements

### 5.1 Frontend Stack
- **HTML5, CSS3, JavaScript (ES6+)**
- **Responsive framework** (Bootstrap/Tailwind)
- **Optimized images** (WebP format, lazy loading)

### 5.2 Integrations
**Calendly Integration:**
- Inline embed of popup
- Custom styling matching NILA branding
- UTM tracking parameters

**n8n Webhook:**
- Endpoint: [te bepalen door ontwikkelteam]
- Authentication: API key header
- Retry logic bij failure
- Success/error handling

### 5.3 Performance
- **Page Speed Score >90**
- **Mobile-friendly test passed**
- **HTTPS only**
- **GDPR compliant cookie handling**

### 5.4 Analytics & Tracking
- Google Analytics 4
- Facebook Pixel (indien applicable)
- Hotjar/heatmap tracking
- Custom events voor CTA conversions

---

## 6. Content Strategy

### 6.1 Tone of Voice
- **Elegant en verfijnd** (consistent met NILA brand)
- **Persoonlijk en warm**
- **Professioneel maar toegankelijk**
- **Focus op craft en kwaliteit**

### 6.2 Key Messages
1. **"Unieke handgemaakte albast verlichting"**
2. **"11 jaar Philips/Signify expertise"**
3. **"Exclusief netwerk van professionals"**
4. **"Van concept tot realisatie"**

### 6.3 Call-to-Action Copy
- CTA 1: "Reserveer uw plek - Exclusieve groepsdemonstratie"
- CTA 2: "Vraag samples aan - Voel de kwaliteit"
- Supporting: "Beperkte plaatsen beschikbaar"

---

## 7. User Experience Flow

### 7.1 Primary Flow - Groepsmeeting
1. User landt op pagina
2. Scrollt door content, ziet sociale proof
3. Klikt "Groepsdemonstratie" CTA
4. Calendly opent (popup/nieuwe tab)
5. Selecteert datum/tijd
6. Vult contactgegevens in
7. Ontvangt bevestiging
8. Email follow-up met details

### 7.2 Secondary Flow - Samples
1. User geïnteresseerd in fysieke ervaring
2. Klikt "Sample aanvragen" CTA  
3. Popup opent met form
4. Vult gegevens in (met validatie)
5. Submit triggert webhook naar n8n
6. Success message + form sluit
7. Confirmation email (via n8n workflow)
8. Samples worden verzonden

### 7.3 Error Handling
- Form validatie met duidelijke error messages
- Webhook failure: retry + fallback email
- Calendly unavailable: alternatieve contact optie
- Laadtijd issues: loading states

---

## 8. Testing Requirements

### 8.1 Functional Testing
- **Cross-browser** (Chrome, Firefox, Safari, Edge)
- **Device testing** (Desktop, tablet, mobile)
- **Form submissions** (happy path + edge cases)
- **Webhook integration** (success/failure scenarios)
- **Calendly integration** (booking flow)

### 8.2 Performance Testing
- **Load testing** (concurrent users)
- **Speed testing** (PageSpeed Insights)
- **Mobile performance** (3G/4G simulation)

### 8.3 A/B Testing Plan
**Test variations:**
- Hero messaging (functional vs emotional)
- CTA button colors (gold vs blue)
- Social proof placement
- Form length (minimal vs detailed)

---

## 9. Launch Plan

### 9.1 Pre-Launch (Week -2)
- Staging environment setup
- Content review & approval
- Technical integration testing
- Analytics implementation

### 9.2 Soft Launch (Week -1)
- Limited traffic (10% of audience)
- Monitor conversions & technical issues
- Gather initial feedback
- Performance optimization

### 9.3 Full Launch (Week 0)
- Full traffic redirect
- Social media announcement
- Email marketing campaign
- PR outreach (design/architecture media)

### 9.4 Post-Launch (Week ****)
- Daily performance monitoring
- A/B testing implementation
- User feedback collection
- Optimization iterations

---

## 10. Success Criteria & KPIs

### 10.1 Primary KPIs
- **Conversion Rate CTA 1**: >8% (groepsmeeting bookings)
- **Conversion Rate CTA 2**: >12% (sample requests)
- **Overall Page Conversion**: >15%
- **Bounce Rate**: <45%

### 10.2 Secondary KPIs  
- **Time on Page**: >2 minutes
- **Page Speed Score**: >90
- **Mobile Conversion Rate**: >10%
- **Lead Quality Score**: >7/10

### 10.3 Business Impact
- **Qualified leads per month**: +50%
- **Sales pipeline growth**: +30%
- **Average deal size**: Maintain current level
- **Sales cycle reduction**: -2 weeks

---

## 11. Timeline & Milestones

| Week | Milestone | Deliverables |
|------|-----------|--------------|
| 1-2 | Design & Wireframes | Mockups, content strategy |
| 3-4 | Development Sprint 1 | Frontend build, integrations |
| 5-6 | Development Sprint 2 | Testing, optimization |
| 7 | Testing & QA | Cross-browser, performance |
| 8 | Launch Preparation | Staging, final approvals |
| 9 | Soft Launch | Limited traffic, monitoring |
| 10 | Full Launch | Complete rollout |

---

## 12. Budget Considerations

### 12.1 Development Costs
- Frontend development: 40-60 uur
- Integration work: 15-20 uur  
- Testing & QA: 10-15 uur
- Project management: 10-15 uur

### 12.2 Ongoing Costs
- Hosting (premium performance): €50/maand
- Calendly Pro subscription: €10/gebruiker/maand
- Analytics tools: €100/maand
- A/B testing platform: €150/maand

### 12.3 ROI Expectations
- **Break-even**: 8-12 weken na launch
- **Payback period**: 6 maanden
- **Expected ROI Year 1**: 300-500%

---

## 13. Risk Analysis & Mitigation

### 13.1 Technical Risks
**Risk**: Calendly integration issues
**Mitigation**: Backup contact form + manual booking process

**Risk**: Webhook reliability
**Mitigation**: Fallback email notification + retry logic

**Risk**: Performance issues
**Mitigation**: CDN implementation + image optimization

### 13.2 Business Risks  
**Risk**: Low conversion rates
**Mitigation**: Extensive A/B testing plan + rapid optimization

**Risk**: Poor lead quality
**Mitigation**: Qualification questions in forms + lead scoring

### 13.3 Compliance Risks
**Risk**: GDPR violations
**Mitigation**: Privacy policy + explicit consent checkboxes

---

## 14. Appendices

### 14.1 Brand Assets
- Logo variations (SVG format)
- Color codes (HEX, RGB, CMYK)
- Typography specifications
- Image library (high-res, optimized)

### 14.2 Technical Specifications
- Webhook endpoint documentation
- JSON schema validation
- API rate limits
- Security requirements

### 14.3 Content Templates
- Email confirmation templates
- Error message copy
- Loading state messages
- Success confirmation copy

---

**Document Version**: 1.0  
**Created**: 29 Mei 2025  
**Owner**: [Product Manager]  
**Stakeholders**: Marketing, Sales, Development, Design