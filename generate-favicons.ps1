# NILA Favicon Generator Script voor Windows PowerShell
# Vereist: ImageMagick voor Windows

Write-Host "🎨 Generating NILA favicons from Logo_Nila_Zwart.jpg..." -ForegroundColor Green

# Controleer of ImageMagick is geïnstalleerd
try {
    $null = Get-Command magick -ErrorAction Stop
    Write-Host "✅ ImageMagick gevonden" -ForegroundColor Green
} catch {
    Write-Host "❌ ImageMagick is niet geïnstalleerd." -ForegroundColor Red
    Write-Host "Download en installeer ImageMagick van: https://imagemagick.org/script/download.php#windows" -ForegroundColor Yellow
    Write-Host "Of gebruik de online favicon generator: https://realfavicongenerator.net/" -ForegroundColor Yellow
    exit 1
}

# Controleer of het logo bestand bestaat
if (-not (Test-Path "public\Logo_Nila_Zwart.jpg")) {
    Write-Host "❌ Logo bestand niet gevonden: public\Logo_Nila_Zwart.jpg" -ForegroundColor Red
    exit 1
}

Set-Location public

Write-Host "📐 Generating favicon.ico (16x16, 32x32, 48x48)..." -ForegroundColor Cyan
magick Logo_Nila_Zwart.jpg -resize 48x48 -background white -gravity center -extent 48x48 favicon-48.png
magick Logo_Nila_Zwart.jpg -resize 32x32 -background white -gravity center -extent 32x32 favicon-32x32.png
magick Logo_Nila_Zwart.jpg -resize 16x16 -background white -gravity center -extent 16x16 favicon-16x16.png
magick favicon-16x16.png favicon-32x32.png favicon-48.png favicon.ico

Write-Host "🍎 Generating Apple Touch Icon (180x180)..." -ForegroundColor Cyan
magick Logo_Nila_Zwart.jpg -resize 180x180 -background white -gravity center -extent 180x180 apple-touch-icon.png

Write-Host "🤖 Generating Android Chrome icons..." -ForegroundColor Cyan
magick Logo_Nila_Zwart.jpg -resize 192x192 -background transparent -gravity center -extent 192x192 android-chrome-192x192.png
magick Logo_Nila_Zwart.jpg -resize 512x512 -background transparent -gravity center -extent 512x512 android-chrome-512x512.png

Write-Host "🧹 Cleaning up temporary files..." -ForegroundColor Cyan
Remove-Item favicon-48.png -ErrorAction SilentlyContinue

Set-Location ..

Write-Host "✅ Favicons generated successfully!" -ForegroundColor Green
Write-Host "📁 Generated files:" -ForegroundColor Yellow
Write-Host "   - favicon.ico"
Write-Host "   - favicon-16x16.png"
Write-Host "   - favicon-32x32.png"
Write-Host "   - apple-touch-icon.png"
Write-Host "   - android-chrome-192x192.png"
Write-Host "   - android-chrome-512x512.png"
Write-Host "   - site.webmanifest"
Write-Host "🚀 Ready for deployment!" -ForegroundColor Green
