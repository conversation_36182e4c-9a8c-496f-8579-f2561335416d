# NILA Sales Landing Page - Poetry of Light

Een high-converting landingspagina voor NILA's exclusieve handgemaakte albast verlichting, ontworpen om prospects naar twee hoofdacties te leiden: groepsdemonstraties boeken en product samples aanvragen.

## 🎯 Project Overzicht

Deze landingspagina behoudt de elegante esthetiek Van wearenila.com terwijl het geoptimaliseerd is voor conversie. Het project is gebouwd met moderne web technologieën en volgt de specificaties uit het Product Requirements Document (PRD).

## 🚀 Snelle Start

### Vereisten
- Node.js (versie 16 of hoger)
- npm of yarn

### Installatie
```bash
# Clone het project
git clone https://github.com/RickyRic248/NILA-salepage.git
cd NILA-salepage

# Installeer dependencies
npm install

# Start development server
npm run dev
```

De website is nu beschikbaar op `http://localhost:3000`

## 🛠️ Beschikbare Scripts

```bash
npm run dev      # Start development server
npm run build    # Build voor productie
npm run preview  # Preview productie build
npm run serve    # Serve productie build op poort 3000
```

## 📁 Project Structuur

```
NILA-salepage/
├── src/
│   ├── assets/
│   │   ├── images/          # Afbeeldingen en logo's
│   │   └── fonts/           # Custom fonts (indien nodig)
│   ├── styles/
│   │   └── main.css         # Hoofdstijlen met Tailwind
│   ├── scripts/
│   │   └── main.js          # JavaScript functionaliteit
│   └── components/          # Herbruikbare componenten
├── public/                  # Statische bestanden
├── projectplan/             # Project documentatie
├── index.html              # Hoofdpagina
├── package.json
├── tailwind.config.js      # Tailwind configuratie
├── vite.config.js          # Vite configuratie
└── README.md
```

## 🎨 Design System

### Kleurenpalet (NILA Brand)
- **Warm White**: `#FAF8F5` - Primaire achtergrondkleur
- **Navy**: `#1E2A5E` - Hoofdtekst en accenten
- **Gold**: `#B8860B` - CTA buttons en highlights
- **Dark Gray**: `#2C2C2C` - Body tekst
- **Accent Gold**: `#D4AF37` - Hover states en details

### Typography
- **Headers**: Playfair Display (serif)
- **Body**: Montserrat (sans-serif)

## 🔧 Configuratie

### Environment Variables Setup

1. **Kopieer environment template:**
```bash
cp .env.example .env
```

2. **Vul de juiste waarden in `.env`:**
```bash
# Cal.com API Configuration
VITE_CALCOM_API_TOKEN=your_calcom_api_token_here
VITE_CALCOM_BASE_URL=https://api.cal.com/v2
VITE_CALCOM_USERNAME=your-calcom-username
VITE_CALCOM_EVENT_SLUG=your-event-slug
VITE_CALCOM_URL=https://cal.com/your-username/your-event

# Webhook Configuration (optioneel)
VITE_WEBHOOK_URL=https://your-webhook-url.com
VITE_WEBHOOK_API_KEY=your-webhook-api-key
```

### Cal.com Integratie
De Cal.com configuratie wordt nu automatisch geladen uit environment variables voor betere beveiliging.

### Webhook Configuratie
Webhook instellingen worden ook via environment variables geconfigureerd.

### Analytics
Configureer Google Analytics en Facebook Pixel:
```javascript
const CONFIG = {
    analytics: {
        gtag: 'GA_MEASUREMENT_ID',
        fbPixel: 'FB_PIXEL_ID'
    }
};
```

## 📊 Tracking & Analytics

De landingspagina tracked automatisch:
- **CTA clicks** (groepsdemonstratie en samples)
- **Form submissions** (sample aanvragen)
- **Scroll behavior** en **time on page**
- **Conversion events** voor beide CTA's

## 🔒 GDPR Compliance

- Expliciete consent checkbox in sample formulier
- Cookie consent handling
- Privacy policy links
- Data minimalisatie in formulieren

## 📱 Responsive Design

De website is volledig responsive en geoptimaliseerd voor:
- **Desktop** (1440px+)
- **Laptop** (1024px - 1439px)
- **Tablet** (768px - 1023px)
- **Mobile** (320px - 767px)

## ⚡ Performance

Doelstellingen:
- **Page Speed Score**: >90
- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **Mobile-friendly**: 100%

## 🧪 Testing

### Cross-browser Testing
- Chrome (laatste 2 versies)
- Firefox (laatste 2 versies)
- Safari (laatste 2 versies)
- Edge (laatste 2 versies)

### Functioneel Testen
- Form validatie en submission
- Calendly integratie
- Webhook connectivity
- Responsive design
- Performance metrics

## 🚀 Deployment

### Productie Build
```bash
npm run build
```

### Vercel Deployment

Voor Vercel deployment, voeg de volgende environment variables toe in je Vercel dashboard:

**Environment Variables in Vercel:**
- `VITE_CALCOM_API_TOKEN` - Je Cal.com API token
- `VITE_CALCOM_BASE_URL` - `https://api.cal.com/v2`
- `VITE_CALCOM_USERNAME` - Je Cal.com gebruikersnaam
- `VITE_CALCOM_EVENT_SLUG` - Je Cal.com event slug
- `VITE_CALCOM_URL` - Je volledige Cal.com URL
- `VITE_RESEND_API_KEY` - Je Resend API key (aanbevolen voor emails)
- `VITE_WEBHOOK_URL` - Je webhook URL (fallback)
- `VITE_WEBHOOK_API_KEY` - Je webhook API key (fallback)

**Stappen:**
1. Ga naar je Vercel project dashboard
2. Klik op "Settings" → "Environment Variables"
3. Voeg alle bovenstaande variabelen toe
4. Deploy je project

### Staging Environment
Voor staging deployment, zorg ervoor dat:
1. Webhook URLs verwijzen naar test environment
2. Analytics tracking is geconfigureerd voor staging
3. Cal.com URLs verwijzen naar test calendars

## 📈 Success Metrics

### Primaire KPI's
- **Conversion Rate CTA 1**: >8% (groepsmeeting bookings)
- **Conversion Rate CTA 2**: >12% (sample requests)
- **Overall Page Conversion**: >15%
- **Bounce Rate**: <45%

### Secundaire KPI's
- **Time on Page**: >2 minuten
- **Page Speed Score**: >90
- **Mobile Conversion Rate**: >10%
- **Lead Quality Score**: >7/10

## 🔄 A/B Testing Plan

Geplande test variaties:
- Hero messaging (functioneel vs emotioneel)
- CTA button kleuren (goud vs blauw)
- Social proof plaatsing
- Formulier lengte (minimaal vs gedetailleerd)

## 🐛 Troubleshooting

### Veelvoorkomende Problemen

**Calendly laadt niet:**
- Controleer of Calendly script correct geladen is
- Verificeer Calendly URL configuratie
- Check browser console voor JavaScript errors

**Webhook fails:**
- Controleer webhook URL en API key
- Verificeer n8n workflow status
- Check network connectivity

**Styling problemen:**
- Run `npm run build` om Tailwind CSS te regenereren
- Clear browser cache
- Controleer Tailwind configuratie

## 📞 Support

Voor technische vragen of ondersteuning:
- **Email**: <EMAIL>
- **Project Repository**: https://github.com/RickyRic248/NILA-salepage

## 📄 Licentie

Dit project is eigendom van NILA - Poetry of Light. Alle rechten voorbehouden.

---

**Versie**: 1.0.1
**Laatste Update**: 29 Mei 2025
**Ontwikkeld door**: NILA Development Team
