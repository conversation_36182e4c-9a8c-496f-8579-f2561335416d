/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx,html}",
  ],
  theme: {
    extend: {
      colors: {
        // NILA Brand Colors (from official brand book)
        'nila': {
          'alabaster-white': '#F8F4EC',  // PANTONE 11-0103 TCX
          'natural-beige': '#DCC8B6',    // PANTONE 482 C
          'rose': '#CFB5A7',             // PANTONE 4745 C
          'night-black': '#373534',      // PANTONE Black 7 C
          // Legacy colors for gradual transition
          'warm-white': '#F8F4EC',
          'navy': '#373534',
          'gold': '#DCC8B6',
          'dark-gray': '#373534',
          'light-gray': '#F8F4EC',
          'accent-gold': '#CFB5A7'
        }
      },
      fontFamily: {
        'serif': ['IBM Plex Sans', 'sans-serif'],     // Headers - IBM Plex Sans Regular
        'sans': ['DM Sans', 'sans-serif'],            // Body text - DM Sans Light
        'subheading': ['DM Sans', 'sans-serif'],      // Subheaders - DM Sans SemiBold
      },
      fontSize: {
        'hero': ['3.5rem', { lineHeight: '1.1', letterSpacing: '-0.02em' }],
        'section-title': ['2.5rem', { lineHeight: '1.2', letterSpacing: '-0.01em' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-in-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'pulse-gold': 'pulseGold 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(30px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseGold: {
          '0%, 100%': { boxShadow: '0 0 0 0 rgba(212, 175, 55, 0.7)' },
          '50%': { boxShadow: '0 0 0 10px rgba(212, 175, 55, 0)' },
        },
      },
      backgroundImage: {
        'gradient-beige': 'linear-gradient(135deg, #DCC8B6 0%, #CFB5A7 100%)',
        'gradient-night': 'linear-gradient(135deg, #373534 0%, #2C2C2C 100%)',
        // Legacy gradients for gradual transition
        'gradient-gold': 'linear-gradient(135deg, #DCC8B6 0%, #CFB5A7 100%)',
        'gradient-navy': 'linear-gradient(135deg, #373534 0%, #2C2C2C 100%)',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
