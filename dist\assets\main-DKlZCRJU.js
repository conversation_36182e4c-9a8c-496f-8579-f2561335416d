(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const l of s.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&a(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function a(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();const p={calcom:{url:"https://cal.com/nila-poetry-of-light/nila-demo",apiToken:"cal_live_1dc5e2e1320cf007c1dff0e00e9d458a",baseUrl:"https://api.cal.com/v2",username:"nila-poetry-of-light",eventTypeSlug:"nila-demo"},analytics:{googleAnalytics:{enabled:!1,measurementId:null},facebookPixel:{enabled:!1,pixelId:null},hotjar:{enabled:!1,siteId:null}}};window.openGroupDemo=function(){console.log("🗓️ Opening Cal.com booking..."),typeof m=="function"&&m("cta_click",{cta_type:"group_demo",cta_location:"hero"});try{console.log("✅ Triggering Cal.com popup via data attributes...");const e=document.createElement("button");e.setAttribute("data-cal-link","nila-poetry-of-light/nila-demo"),e.setAttribute("data-cal-config",'{"layout":"month_view","theme":"light"}'),e.style.display="none",document.body.appendChild(e),e.click(),setTimeout(()=>{document.body.removeChild(e)},100),setTimeout(()=>{b()},500)}catch(e){console.error("❌ Cal.com error:",e),console.log("🔄 Falling back to direct URL..."),window.open("https://cal.com/nila-poetry-of-light/nila-demo","_blank")}};window.openSampleForm=function(){console.log("📦 Opening sample form..."),typeof m=="function"&&m("cta_click",{cta_type:"sample_request",cta_location:"hero"});const e=document.getElementById("sample-modal");if(e){e.classList.remove("hidden"),document.body.style.overflow="hidden";const t=e.querySelector("input");t&&setTimeout(()=>t.focus(),100)}};window.closeSampleForm=function(){console.log("❌ Closing sample form...");const e=document.getElementById("sample-modal");e&&(e.classList.add("hidden"),document.body.style.overflow="")};function b(){try{const e=document.querySelector("[data-cal-namespace]"),t=document.querySelector(".cal-modal-overlay, [data-cal-namespace] + div"),n=document.querySelector("[data-cal-namespace] iframe");if(e){const o=e.querySelector("div");o&&(o.style.background="#F8F4EC",o.style.borderRadius="1.5rem",o.style.overflow="hidden",o.style.boxShadow="0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3)",o.style.border="none",o.style.maxWidth="85vw",o.style.maxHeight="85vh")}t&&(t.style.background="rgba(55, 53, 52, 0.85)",t.style.backdropFilter="blur(12px)",t.style.webkitBackdropFilter="blur(12px)"),n&&(n.style.borderRadius="1.5rem",n.style.background="#F8F4EC",n.addEventListener("load",()=>{try{v(n)}catch{console.log("Cannot access iframe content (CORS)")}})),document.querySelectorAll('[data-cal-namespace] button[aria-label*="close"], [data-cal-namespace] .close, [data-cal-namespace] [data-testid="close"]').forEach(o=>{o.style.background="#F8F4EC",o.style.color="#373534",o.style.borderRadius="50%",o.style.width="44px",o.style.height="44px",o.style.border="2px solid #DCC8B6",o.style.transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",o.style.boxShadow="0 4px 12px rgba(55, 53, 52, 0.15)",o.style.position="absolute",o.style.top="1rem",o.style.right="1rem",o.style.zIndex="10",o.addEventListener("mouseenter",()=>{o.style.background="#CFB5A7",o.style.transform="scale(1.05)",o.style.borderColor="#CFB5A7",o.style.boxShadow="0 8px 20px rgba(207, 181, 167, 0.4)"}),o.addEventListener("mouseleave",()=>{o.style.background="#F8F4EC",o.style.transform="scale(1)",o.style.borderColor="#DCC8B6",o.style.boxShadow="0 4px 12px rgba(55, 53, 52, 0.15)"})}),console.log("✅ Enhanced Cal.com styles applied")}catch(e){console.error("❌ Error applying custom Cal.com styles:",e)}}function v(e){try{const t=e.contentDocument||e.contentWindow.document;if(!t)return;t.head.insertAdjacentHTML("beforeend",`
            <style>
                /* NILA Custom styles for Cal.com iframe content */
                body {
                    font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    background: #F8F4EC !important;
                }

                /* Header styling */
                h1, h2, h3 {
                    color: #373534 !important;
                    font-family: 'IBM Plex Sans', sans-serif !important;
                }

                /* Button styling */
                button[data-testid*="time"] {
                    border: 2px solid #DCC8B6 !important;
                    background: #F8F4EC !important;
                    color: #373534 !important;
                    border-radius: 0.75rem !important;
                    transition: all 0.3s ease !important;
                }

                button[data-testid*="time"]:hover {
                    background: #CFB5A7 !important;
                    border-color: #CFB5A7 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 4px 12px rgba(207, 181, 167, 0.3) !important;
                }

                /* Selected date styling */
                [data-testid*="day"][aria-selected="true"] {
                    background: #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                }

                /* Primary buttons */
                button[type="submit"], .btn-primary {
                    background: #CFB5A7 !important;
                    border: 2px solid #CFB5A7 !important;
                    color: white !important;
                    border-radius: 0.75rem !important;
                    font-weight: 600 !important;
                    transition: all 0.3s ease !important;
                }

                button[type="submit"]:hover, .btn-primary:hover {
                    background: #373534 !important;
                    border-color: #373534 !important;
                    transform: translateY(-2px) !important;
                    box-shadow: 0 8px 20px rgba(55, 53, 52, 0.3) !important;
                }
            </style>
        `),console.log("✅ Iframe styles injected")}catch{console.log("Cannot inject iframe styles (CORS restriction)")}}function k(){if(document.getElementById("nila-calcom-custom-css"))return;const e=`
        /* NILA Custom Cal.com Styling - Enhanced */
        [data-cal-namespace] {
            z-index: 9999 !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        /* Main popup container */
        [data-cal-namespace] > div {
            border-radius: 1.5rem !important;
            box-shadow: 0 25px 50px -12px rgba(55, 53, 52, 0.4), 0 0 0 1px rgba(220, 200, 182, 0.3) !important;
            overflow: hidden !important;
            border: none !important;
            max-width: 85vw !important;
            max-height: 85vh !important;
            background: #F8F4EC !important;
        }

        /* Backdrop overlay */
        [data-cal-namespace] + div {
            background: rgba(55, 53, 52, 0.85) !important;
            backdrop-filter: blur(12px) !important;
            -webkit-backdrop-filter: blur(12px) !important;
        }

        /* Main iframe styling */
        [data-cal-namespace] iframe {
            border-radius: 1.5rem !important;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            background: #F8F4EC !important;
        }

        /* Close button styling */
        [data-cal-namespace] button[aria-label*="close"],
        [data-cal-namespace] .close,
        [data-cal-namespace] [data-testid="close"] {
            background: #F8F4EC !important;
            color: #373534 !important;
            border-radius: 50% !important;
            width: 44px !important;
            height: 44px !important;
            border: 2px solid #DCC8B6 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 4px 12px rgba(55, 53, 52, 0.15) !important;
            position: absolute !important;
            top: 1rem !important;
            right: 1rem !important;
            z-index: 10 !important;
        }

        [data-cal-namespace] button[aria-label*="close"]:hover,
        [data-cal-namespace] .close:hover,
        [data-cal-namespace] [data-testid="close"]:hover {
            background: #CFB5A7 !important;
            transform: scale(1.05) !important;
            border-color: #CFB5A7 !important;
            box-shadow: 0 8px 20px rgba(207, 181, 167, 0.4) !important;
        }

        /* Header styling inside iframe */
        [data-cal-namespace] iframe[src*="cal.com"] {
            /* Custom CSS will be injected into iframe content */
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            [data-cal-namespace] > div {
                margin: 0.75rem !important;
                max-width: calc(100vw - 1.5rem) !important;
                max-height: calc(100vh - 1.5rem) !important;
                border-radius: 1.25rem !important;
            }

            [data-cal-namespace] button[aria-label*="close"],
            [data-cal-namespace] .close,
            [data-cal-namespace] [data-testid="close"] {
                width: 40px !important;
                height: 40px !important;
                top: 0.75rem !important;
                right: 0.75rem !important;
            }
        }

        /* Animation for popup appearance */
        [data-cal-namespace] > div {
            animation: nilaPopupAppear 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        @keyframes nilaPopupAppear {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Enhanced backdrop animation */
        [data-cal-namespace] + div {
            animation: nilaBackdropAppear 0.3s ease-out !important;
        }

        @keyframes nilaBackdropAppear {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
    `,t=document.createElement("style");t.id="nila-calcom-custom-css",t.textContent=e,document.head.appendChild(t),console.log("✅ Custom Cal.com CSS injected")}function x(){new MutationObserver(t=>{t.forEach(n=>{n.addedNodes.forEach(a=>{a.nodeType===Node.ELEMENT_NODE&&(a.hasAttribute&&a.hasAttribute("data-cal-namespace")&&(console.log("🎨 Cal.com popup detected, applying custom styles..."),setTimeout(()=>{b()},100)),a.querySelector&&a.querySelector("[data-cal-namespace]")&&(console.log("🎨 Cal.com popup detected in child, applying custom styles..."),setTimeout(()=>{b()},100)))})})}).observe(document.body,{childList:!0,subtree:!0}),console.log("👀 Cal.com style monitor initialized")}const r={sampleModal:document.getElementById("sample-modal"),sampleForm:document.getElementById("sample-form"),submitButton:document.getElementById("submit-sample-form"),submitText:document.getElementById("submit-text"),submitLoading:document.getElementById("submit-loading"),floatingCta:document.getElementById("floating-cta"),stickyCtaBar:document.getElementById("sticky-cta-bar")};console.log("🧪 Adding immediate scroll test...");window.addEventListener("scroll",function(){console.log("🔄 IMMEDIATE SCROLL TEST - Position:",window.scrollY)});console.log("✅ Immediate scroll test added");document.addEventListener("DOMContentLoaded",function(){E()});function E(){console.log("🎨 NILA Landing Page - Initializing..."),r.sampleModal&&(r.sampleModal.classList.add("hidden"),document.body.style.overflow=""),L(),F(),M(),q(),console.log("🚀 About to initialize sticky CTA bar..."),_(),console.log("✅ Sticky CTA bar initialization completed"),N(),P(),k(),x(),console.log("✅ NILA Landing Page - Ready!")}function L(){const e={threshold:.1,rootMargin:"0px 0px -50px 0px"},t=new IntersectionObserver(a=>{a.forEach(o=>{o.isIntersecting&&o.target.classList.add("visible")})},e);document.querySelectorAll(".animate-on-scroll").forEach(a=>{t.observe(a)}),A();const n=document.querySelector(".nederlands-vakmanschap-image");n&&new IntersectionObserver(o=>{o.forEach(s=>{s.isIntersecting?(console.log("Nederlands vakmanschap image is in view - applying zoom"),s.target.style.transform="scale(1.15)",s.target.style.objectPosition="33% 50%"):s.target.style.transform="scale(1.0)"})},{threshold:.2,rootMargin:"0px 0px -100px 0px"}).observe(n)}function A(){const e=document.querySelectorAll("[data-count-to]"),t=new IntersectionObserver(n=>{n.forEach(a=>{a.isIntersecting&&!a.target.classList.contains("counted")&&(a.target.classList.add("counted"),T(a.target))})},{threshold:.5,rootMargin:"0px 0px -100px 0px"});e.forEach(n=>{t.observe(n)})}function T(e){const t=parseInt(e.getAttribute("data-count-to")),n=e.getAttribute("data-count-suffix")||"",a=2e3,o=performance.now(),s=0;function l(d){const u=d-o,i=Math.min(u/a,1),c=1-Math.pow(1-i,4),f=Math.floor(s+(t-s)*c);e.textContent=f+n,i<1?requestAnimationFrame(l):e.textContent=t+n}requestAnimationFrame(l)}function F(){const e=r.sampleForm;if(!e)return;e.querySelectorAll("input[required], select[required]").forEach(n=>{n.addEventListener("blur",C),n.addEventListener("input",I)})}function C(e){const t=e.target,n=t.value.trim();return t.classList.remove("border-red-500"),t.hasAttribute("required")&&!n?(y(t,"Dit veld is verplicht"),!1):t.type==="email"&&n&&!B(n)?(y(t,"Voer een geldig e-mailadres in"),!1):t.name==="postalCode"&&n&&!D(n)?(y(t,"Voer een geldige postcode in"),!1):!0}function I(e){const t=e.target;t.classList.remove("border-red-500");const n=t.parentNode.querySelector(".error-message");n&&n.remove()}function y(e,t){e.classList.add("border-red-500");const n=e.parentNode.querySelector(".error-message");n&&n.remove();const a=document.createElement("div");a.className="error-message text-red-500 text-sm mt-1",a.textContent=t,e.parentNode.appendChild(a)}function B(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function D(e){return/^[1-9][0-9]{3}\s?[A-Za-z]{2}$/.test(e)}function M(){r.sampleForm&&r.sampleForm.addEventListener("submit",R),r.sampleModal&&r.sampleModal.addEventListener("click",e=>{e.target===r.sampleModal&&closeSampleForm()}),document.addEventListener("keydown",e=>{e.key==="Escape"&&!r.sampleModal.classList.contains("hidden")&&closeSampleForm()})}function N(){const e=document.querySelector(".hero-image");if(!e)return;let t=!1;function n(){const o=window.pageYOffset,l=document.getElementById("hero").offsetHeight;if(o<l){const u=o*.5;e.style.transform=`scale(1.4) translateY(${u}px)`;const i=Math.max(.8,1-o/l*.2);e.style.opacity=i}t=!1}function a(){t||(requestAnimationFrame(n),t=!0)}window.addEventListener("scroll",a,{passive:!0})}function q(){if(!r.floatingCta)return;let e=!1;function t(){const o=window.scrollY,s=window.innerHeight;o>s&&!e?(r.floatingCta.classList.remove("hidden"),e=!0,m("floating_cta_shown",{scroll_position:o})):o<=s&&e&&(r.floatingCta.classList.add("hidden"),e=!1)}let n=!1;function a(){n||(requestAnimationFrame(()=>{t(),n=!1}),n=!0)}window.addEventListener("scroll",a),r.floatingCta.addEventListener("click",()=>{m("floating_cta_click",{cta_type:"floating_demo_button"})})}function _(){console.log("🔧 Initializing sticky CTA bar...");const e=document.getElementById("sticky-cta-bar")||r.stickyCtaBar;if(!e){console.error("❌ Sticky CTA bar element not found!"),console.log("Available elements:",Object.keys(r));return}console.log("✅ Sticky CTA bar element found:",e),r.stickyCtaBar=e;let t=!1,n=!1;function a(){const l=window.scrollY,d=window.innerWidth<769,u=d?200:300,i=document.documentElement.scrollHeight,c=window.innerHeight,g=l+c>i-200;console.log("🔄 SCROLL EVENT DETECTED:",{scrollPosition:l,triggerPoint:u,isVisible:t,nearBottom:g,isMobile:d,shouldShow:l>u&&!t&&!g,elementExists:!!r.stickyCtaBar}),l>u&&!t&&!g?(console.log("✅ Showing sticky CTA bar"),r.stickyCtaBar.classList.remove("hidden"),r.stickyCtaBar.classList.remove("translate-y-full"),r.stickyCtaBar.style.transform="translateY(0)",r.stickyCtaBar.style.display="block",t=!0,n||(m("sticky_cta_shown",{scroll_position:l,is_mobile:d,trigger_point:u}),n=!0)):(l<=u||g)&&t&&(console.log("❌ Hiding sticky CTA bar"),r.stickyCtaBar.classList.add("translate-y-full"),r.stickyCtaBar.style.transform="translateY(100%)",setTimeout(()=>{t||r.stickyCtaBar.classList.add("hidden")},300),t=!1)}let o=!1;function s(){o||(requestAnimationFrame(()=>{a(),o=!1}),o=!0)}window.addEventListener("scroll",s),console.log("✅ Sticky CTA bar scroll listener added"),window.addEventListener("scroll",()=>{console.log("🔄 Basic scroll event detected at:",window.scrollY)}),console.log("🧪 Initial scroll position:",window.scrollY),console.log("🧪 Initial trigger test..."),a()}window.hideStickyBar=function(){r.stickyCtaBar&&(r.stickyCtaBar.style.transform="translateY(100%)",setTimeout(()=>{r.stickyCtaBar.classList.add("hidden")},300),m("sticky_cta_dismissed",{user_action:!0}))};function P(){console.log("🗓️ Initializing Cal.com integration..."),typeof Cal<"u"?(Cal("init",{origin:"https://app.cal.com"}),Cal("ui",{styles:{branding:{brandColor:"#CFB5A7"}},hideEventTypeDetails:!1,layout:"month_view"}),console.log("✅ Cal.com initialized")):console.log("⚠️ Cal.com script not loaded yet"),h(),setInterval(h,10*60*1e3),console.log("✅ Cal.com integration initialized")}async function h(){console.log("🗓️ Updating demo times from Cal.com API...");try{const e=new Date().toISOString().split("T")[0],t=new Date(Date.now()+7*24*60*60*1e3).toISOString().split("T")[0],n=await fetch(`${p.calcom.baseUrl}/slots?username=${p.calcom.username}&eventTypeSlug=${p.calcom.eventTypeSlug}&start=${e}&end=${t}&timeZone=Europe/Amsterdam`,{headers:{Authorization:`Bearer ${p.calcom.apiToken}`,"cal-api-version":"2024-09-04","Content-Type":"application/json"}});if(n.ok){const a=await n.json();if(console.log("✅ Cal.com API response:",a),a.status==="success"&&a.data){let o=null,s=null;for(const[l,d]of Object.entries(a.data))if(d&&d.length>0){o=d[0],s=new Date(o.start);break}if(o&&s){document.querySelectorAll("[data-demo-time]").forEach(i=>{const c=i.getAttribute("data-demo-format");c==="week"?i.textContent=`Volgende demonstratie ${j(s)}`:c==="short"?i.textContent=$(s):i.textContent=z(s)}),document.querySelectorAll("[data-demo-availability]").forEach(i=>{if(i.hasAttribute("data-hero-urgency")){const c=Object.values(a.data).reduce((f,g)=>f+g.length,0);c<=3?i.textContent="Nog slechts 3 plaatsen beschikbaar":c<=10?i.textContent="Beperkte plaatsen beschikbaar":i.textContent="Plaatsen beschikbaar"}else i.textContent="Neem deel en ontvang een kortingscode"}),document.querySelectorAll("[data-demo-urgency]").forEach(i=>{const c=Object.values(a.data).reduce((f,g)=>f+g.length,0);c<=3||c<=10,i.textContent="Neem deel en ontvang een kortingscode",i.classList.add("text-green-400")}),console.log("✅ Demo times updated from Cal.com API");return}else console.log("⚠️ No available slots found in Cal.com API response")}}else console.warn("⚠️ Cal.com API error:",n.status,n.statusText)}catch(e){console.error("❌ Error fetching Cal.com data:",e)}console.log("🔄 Using fallback demo times..."),O()}function O(){const e=new Date;e.setDate(e.getDate()+1),e.setHours(14,0,0,0),document.querySelectorAll("[data-demo-time]").forEach(o=>{const s=o.getAttribute("data-demo-format");s==="week"?o.textContent="Volgende demonstratie morgen":o.textContent="Morgen 14:00"}),document.querySelectorAll("[data-demo-availability]").forEach(o=>{o.hasAttribute("data-hero-urgency")?o.textContent="Nog slechts 3 plaatsen beschikbaar":o.textContent="Neem deel en ontvang een kortingscode"}),document.querySelectorAll("[data-demo-urgency]").forEach(o=>{o.textContent="Neem deel en ontvang een kortingscode",o.classList.add("text-green-400")})}function z(e){const t=new Date,n=new Date(t);return n.setDate(n.getDate()+1),e.toDateString()===t.toDateString()?`Vandaag ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toDateString()===n.toDateString()?`Morgen ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toLocaleDateString("nl-NL",{weekday:"long",day:"numeric",month:"long",hour:"2-digit",minute:"2-digit"})}function $(e){const t=new Date,n=new Date(t);return n.setDate(n.getDate()+1),e.toDateString()===t.toDateString()?`Vandaag ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toDateString()===n.toDateString()?`Morgen ${e.toLocaleTimeString("nl-NL",{hour:"2-digit",minute:"2-digit"})}`:e.toLocaleDateString("nl-NL",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"})}function j(e){const t=new Date,n=new Date(t);if(n.setDate(n.getDate()+1),e.toDateString()===t.toDateString())return"vandaag";if(e.toDateString()===n.toDateString())return"morgen";{const a=Math.ceil((e-t)/864e5);return a<=7?`over ${a} dagen`:e.toLocaleDateString("nl-NL",{day:"numeric",month:"long"})}}async function R(e){if(e.preventDefault(),console.log("📝 Submitting sample form..."),!Y()){console.log("❌ Form validation failed");return}w(!0);try{const t=new FormData(r.sampleForm),n=H(t),a=await S(n);if(a.success)console.log("✅ Sample request submitted successfully"),V(),m("form_submit",{form_type:"sample_request",success:!0});else throw new Error(a.error||"Submission failed")}catch(t){console.error("❌ Sample form submission error:",t),W(t.message),m("form_submit",{form_type:"sample_request",success:!1,error:t.message})}finally{w(!1)}}function Y(){const e=r.sampleForm.querySelectorAll("input[required], select[required]");let t=!0;e.forEach(a=>{C({target:a})||(t=!1)});const n=document.getElementById("gdprConsent");return n.checked||(y(n,"U moet akkoord gaan met het privacybeleid"),t=!1),t}function H(e){return{timestamp:new Date().toISOString(),source:"nila_landing_samples",contact:{firstName:e.get("firstName"),lastName:e.get("lastName"),company:e.get("company")||"",email:e.get("email"),phone:e.get("phone")||"",address:{street:e.get("street"),city:e.get("city"),postalCode:e.get("postalCode"),country:e.get("country")}},projectDetails:e.get("projectDetails")||""}}async function S(e,t=0){const n=p.webhook.retryAttempts;try{const a=await fetch(p.webhook.url,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${p.webhook.apiKey}`},body:JSON.stringify(e)});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);return await a.json()}catch(a){if(t<n)return console.log(`🔄 Retrying webhook submission (${t+1}/${n})...`),await new Promise(o=>setTimeout(o,p.webhook.retryDelay*(t+1))),S(e,t+1);throw a}}function w(e){e?(r.submitText.classList.add("hidden"),r.submitLoading.classList.remove("hidden"),r.submitButton.disabled=!0):(r.submitText.classList.remove("hidden"),r.submitLoading.classList.add("hidden"),r.submitButton.disabled=!1)}function V(){const e=r.sampleForm.parentNode;e.innerHTML=`
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h3 class="text-xl font-bold text-nila-navy mb-2">Bedankt voor uw aanvraag!</h3>
            <p class="text-nila-dark-gray mb-6">
                Uw sample aanvraag is succesvol verzonden. U ontvangt binnen 24 uur een bevestiging
                per e-mail en uw samples worden binnen 3-5 werkdagen verzonden.
            </p>
            <button onclick="closeSampleForm()" class="btn-primary">
                Sluiten
            </button>
        </div>
    `,setTimeout(()=>{closeSampleForm()},5e3)}function W(e){const t=document.createElement("div");t.className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",t.innerHTML=`
        <strong>Er is een fout opgetreden:</strong> ${e}
        <br><small>Probeer het opnieuw of neem contact met ons op.</small>
    `,r.sampleForm.insertBefore(t,r.sampleForm.firstChild),setTimeout(()=>{t.parentNode&&t.remove()},1e4)}function m(e,t={}){console.log(`📊 Tracking event: ${e}`,t),typeof gtag<"u"&&gtag("event",e,t),typeof fbq<"u"&&fbq("track",e,t)}function U(e){const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth",block:"start"})}window.testStickyBar=function(){console.log("🧪 Testing sticky CTA bar...");const e=document.getElementById("sticky-cta-bar");return e?(console.log("✅ Sticky bar found:",e),console.log("Current classes:",e.className),console.log("Current style:",e.style.cssText),console.log("Computed style display:",window.getComputedStyle(e).display),console.log("Computed style transform:",window.getComputedStyle(e).transform),console.log("Screen width:",window.innerWidth),console.log("Is desktop?",window.innerWidth>=769),e.classList.remove("hidden"),e.style.transform="translateY(0)",e.style.display="block",console.log("✅ Sticky bar forced to show"),setTimeout(()=>{const t=e.getBoundingClientRect();console.log("Element position:",t),console.log("Is visible?",t.height>0&&t.width>0)},100),{success:!0,element:e}):(console.error("❌ Sticky bar not found"),{success:!1,error:"Element not found"})};window.showStickyBar=function(){const e=document.getElementById("sticky-cta-bar");return e?(e.classList.remove("hidden"),e.style.transform="translateY(0)",e.style.display="block",console.log("✅ Sticky bar force shown"),!0):!1};window.testScrollTrigger=function(){console.log("🧪 Testing scroll trigger..."),console.log("Current scroll position:",window.scrollY),console.log("Is mobile?",window.innerWidth<769),console.log("Trigger point:",window.innerWidth<769?200:300),console.log("Should show?",window.scrollY>(window.innerWidth<769?200:300)),window.scrollTo(0,window.innerWidth<769?250:350),console.log("✅ Scrolled to trigger point")};window.debugScrollListener=function(){console.log("🔍 Debugging scroll listener...");const e=document.getElementById("sticky-cta-bar");console.log("Element exists?",!!e),e&&(console.log("Element classes:",e.className),console.log("Element style:",e.style.cssText),console.log("Computed display:",window.getComputedStyle(e).display)),console.log("🔄 Manually triggering scroll function..."),window.scrollTo(0,400),setTimeout(()=>{console.log("After scroll - position:",window.scrollY),e&&(console.log("After scroll - classes:",e.className),console.log("After scroll - transform:",e.style.transform),console.log("After scroll - display style:",e.style.display),console.log("After scroll - computed display:",window.getComputedStyle(e).display))},500)};window.forceTriggerSticky=function(){console.log("🔧 Force triggering sticky bar logic...");const e=document.getElementById("sticky-cta-bar");return e?(console.log("Before force trigger:"),console.log("- Classes:",e.className),console.log("- Style:",e.style.cssText),console.log("- Computed display:",window.getComputedStyle(e).display),e.classList.remove("hidden"),e.classList.remove("translate-y-full"),e.style.transform="translateY(0)",e.style.display="block",e.style.visibility="visible",e.style.opacity="1",console.log("After force trigger:"),console.log("- Classes:",e.className),console.log("- Style:",e.style.cssText),console.log("- Computed display:",window.getComputedStyle(e).display),!0):!1};window.NILA={openGroupDemo:window.openGroupDemo,openSampleForm:window.openSampleForm,closeSampleForm:window.closeSampleForm,trackEvent:m,scrollToElement:U,testCalcom:window.testCalcom,testStickyBar:window.testStickyBar,showStickyBar:window.showStickyBar,testScrollTrigger:window.testScrollTrigger,debugScrollListener:window.debugScrollListener,forceTriggerSticky:window.forceTriggerSticky};window.testCalcom=function(){if(console.log("🧪 Testing Cal.com..."),console.log("🔍 Cal available?",typeof Cal<"u"),console.log("🔍 Window.calcomLoaded?",window.calcomLoaded),console.log("🔍 Cal object:",typeof Cal<"u"?Cal:"undefined"),typeof Cal<"u"){console.log("✅ Cal.com script loaded");try{return Cal("init",{origin:"https://app.cal.com"}),Cal("openModal",{calLink:"nila-poetry-of-light/nila-demo"}),{success:!0,message:"Cal.com working!"}}catch(e){return console.error("❌ Error:",e),{success:!1,error:e.message}}}else return console.error("❌ Cal.com script not loaded"),{success:!1,error:"Cal.com script not loaded"}};window.debugCalcom=function(){console.log("🔍 Debug Cal.com state:"),console.log("- typeof Cal:",typeof Cal),console.log("- window.calcomLoaded:",window.calcomLoaded),console.log("- Cal object:",Cal);const e=document.querySelectorAll('script[src*="cal.com"]');return console.log("- Cal.com script tags found:",e.length),e.forEach((t,n)=>{console.log(`  Script ${n+1}:`,t.src,"loaded:",t.readyState)}),{calAvailable:typeof Cal<"u",calcomLoaded:window.calcomLoaded,scriptTags:e.length}};
